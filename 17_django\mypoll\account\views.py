#account/views.py - view 함수들을 정의
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth import login, logout, authenticate, update_session_auth_hash
from django.contrib.auth.decorators import login_required
from django.contrib.auth.forms import PasswordChang<PERSON>Form, AuthenticationForm
from django.urls import reverse
from .forms import CustomUserC<PERSON><PERSON>Form, CustomAuthenticationForm, CustomUserChangeForm

##############################
# 사용자 가입 처리
# 요청 url : account/create
#     GET : 가입 폼 양식을 응답
#     POST : 가입 폼 입력값 검증 및 가입 처리
# view 함수 : create
# 응답: GET : template/account/create.html
#      POST : 가입 성공 - home으로 이동
#             가입 실패 - create.html 응답
##############################

def create(request):
    if request.method == "GET":
        return render(request, "account/create.html", {"form": CustomUserCreationForm()})
    elif request.method == "POST":
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            # 폼이 유효하면 사용자 생성
            user = form.save()
            messages.success(request, f'{user.username}님, 회원가입이 완료되었습니다!')
            return redirect(reverse('home'))
        else:
            # 폼이 유효하지 않으면 오류와 함께 폼 다시 표시
            return render(request, "account/create.html", {"form": form})


##############################
# 사용자 로그인 처리
# 요청 url : account/login
#     GET : 로그인 폼 양식을 응답
#     POST : 로그인 폼 입력값 검증 및 로그인 처리
# view 함수 : login_view
# 응답: GET : template/account/login.html
#      POST : 로그인 성공 - home으로 이동
#             로그인 실패 - login.html 응답
##############################

def login_view(request):
    if request.method == "GET":
        return render(request, "account/login.html", {"form": CustomAuthenticationForm()})
    elif request.method == "POST":
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            # 폼이 유효하면 로그인 처리
            user = form.get_user()
            login(request, user)
            messages.success(request, f'{user.username}님, 환영합니다!')

            # next 파라미터가 있으면 해당 페이지로, 없으면 홈으로
            next_url = request.GET.get('next', reverse('home'))
            return redirect(next_url)
        else:
            # 폼이 유효하지 않으면 오류와 함께 폼 다시 표시
            return render(request, "account/login.html", {"form": form})


##############################
# 사용자 로그아웃 처리
# 요청 url : account/logout
# view 함수 : logout_view
# 응답: 로그아웃 후 home으로 이동
##############################

@login_required
def logout_view(request):
    if request.user.is_authenticated:
        username = request.user.username
        logout(request)
        messages.success(request, f'{username}님, 로그아웃되었습니다.')
    return redirect(reverse('home'))

##############################
# 패스워드 수정 처리 view
#
# 요청 url/account/password_change
# view함수 : password_change
#   - GET : 패스워드 변경 폼을 응답(template/password_change.html)
#   - POST : 패스워드 변경 처리 (template/detail.html - redirect)
##############################

@login_required
def password_change(request):
    if request.method == "GET":
        # PasswordChangeForm을 비밀번호를 변경할 User 모델을 넣어서 생성
        # login_user = get_user(request)
        form = PasswordChangeForm(request.user)
        return render(request, "account/password_change.html", {"form": form})
    elif request.method == "POST":
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            update_session_auth_hash(request, user)  # 비밀번호 변경 시 세션 무효화 방지
            messages.success(request, '비밀번호가 변경되었습니다.')
            return redirect(reverse('account:detail'))
        else:
            return render(request, "account/password_change.html", {"form": form})

##############################
# 회원정보 수정
# 요청 URL : account/update
# view 함수 : user_update
#   GET - 수정 양식 페이지로 이동 (template : account/update.html)
#   POST - 수정 처리 (account/detail/ (detail view) - redirect)
##############################
@login_required
def user_update(request):
    if request.method == "GET":
        form = CustomUserChangeForm(instance=request.user)
        return render(request, "account/update.html", {"form": form})
    else: # POST
        form = CustomUserChangeForm(request.POST, request.FILES, instance=request.user)
        if form.is_valid():
            form.save()
            # 로그인 사용자 정보 갱신
            update_session_auth_hash(request, request.user)
            messages.success(request, '회원정보가 수정되었습니다.')
            return redirect(reverse('account:detail'))
        else: # 검증 실패 -> 수정폼(update.html)으로 이동
            return render(request, "account/update.html", {"form": form})
        

##############################
# 회원 탈퇴 - 삭제 처리
# 요청 url : user_delete
# view 함수 : user_delete
#   - POST - 삭제 처리 (home으로 redirect)
# 응답 : home으로 이동 - redirect
#
##############################
@login_required
def user_delete(request):
    if request.method == "POST":
        request.user.delete()
        messages.success(request, '회원탈퇴가 완료되었습니다.')
        return redirect(reverse('home'))
    else:
        return render(request, "account/delete.html")