#account/views.py - view 함수들을 정의
from django.shortcuts import render
from .forms import CustomUserCreationForm

##############################
# 사용자 가입 처리
# 요청 url : account/create
#     GET : 가입 폼 양식을 응답
#     POST : 가입 폼 입력값 검증 및 가입 처리
# view 함수 : create
# 응답: GET : template/account/create.html
#      POST : 가입 성공 - home으로 이동
#             가입 실패 - create.html 응답
##############################

def create(request):
    if request.method == "GET":
        return render(request, "account/create.html", {"form":CustomUserCreationForm()})
    elif request.method == "POST":
        pass