#account/views.py - view 함수들을 정의
from django.shortcuts import render, redirect
from django.contrib import messages
from django.contrib.auth import authenticate, login, logout
from django.urls import reverse
from .forms import CustomUserCreationForm, CustomAuthenticationForm

##############################
# 사용자 가입 처리
# 요청 url : account/create
#     GET : 가입 폼 양식을 응답
#     POST : 가입 폼 입력값 검증 및 가입 처리
# view 함수 : create
# 응답: GET : template/account/create.html
#      POST : 가입 성공 - home으로 이동
#             가입 실패 - create.html 응답
##############################

def create(request):
    if request.method == "GET":
        return render(request, "account/create.html", {"form": CustomUserCreationForm()})
    elif request.method == "POST":
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            # 폼이 유효하면 사용자 생성
            user = form.save()
            messages.success(request, f'{user.username}님, 회원가입이 완료되었습니다!')
            return redirect(reverse('home'))
        else:
            # 폼이 유효하지 않으면 오류와 함께 폼 다시 표시
            return render(request, "account/create.html", {"form": form})


##############################
# 사용자 로그인 처리
# 요청 url : account/login
#     GET : 로그인 폼 양식을 응답
#     POST : 로그인 폼 입력값 검증 및 로그인 처리
# view 함수 : login_view
# 응답: GET : template/account/login.html
#      POST : 로그인 성공 - home으로 이동
#             로그인 실패 - login.html 응답
##############################

def login_view(request):
    if request.method == "GET":
        return render(request, "account/login.html", {"form": CustomAuthenticationForm()})
    elif request.method == "POST":
        form = CustomAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            # 폼이 유효하면 로그인 처리
            user = form.get_user()
            login(request, user)
            messages.success(request, f'{user.username}님, 환영합니다!')

            # next 파라미터가 있으면 해당 페이지로, 없으면 홈으로
            next_url = request.GET.get('next', reverse('home'))
            return redirect(next_url)
        else:
            # 폼이 유효하지 않으면 오류와 함께 폼 다시 표시
            return render(request, "account/login.html", {"form": form})


##############################
# 사용자 로그아웃 처리
# 요청 url : account/logout
# view 함수 : logout_view
# 응답: 로그아웃 후 home으로 이동
##############################

def logout_view(request):
    if request.user.is_authenticated:
        username = request.user.username
        logout(request)
        messages.success(request, f'{username}님, 로그아웃되었습니다.')
    return redirect(reverse('home'))