#account/views.py - view 함수들을 정의
from django.shortcuts import render, redirect
from django.contrib import messages
from django.urls import reverse
from .forms import CustomUserCreationForm

##############################
# 사용자 가입 처리
# 요청 url : account/create
#     GET : 가입 폼 양식을 응답
#     POST : 가입 폼 입력값 검증 및 가입 처리
# view 함수 : create
# 응답: GET : template/account/create.html
#      POST : 가입 성공 - home으로 이동
#             가입 실패 - create.html 응답
##############################

def create(request):
    if request.method == "GET":
        return render(request, "account/create.html", {"form": CustomUserCreationForm()})
    elif request.method == "POST":
        form = CustomUserCreationForm(request.POST)
        if form.is_valid():
            # 폼이 유효하면 사용자 생성
            user = form.save()
            messages.success(request, f'{user.username}님, 회원가입이 완료되었습니다!')
            return redirect(reverse('home'))
        else:
            # 폼이 유효하지 않으면 오류와 함께 폼 다시 표시
            return render(request, "account/create.html", {"form": form})