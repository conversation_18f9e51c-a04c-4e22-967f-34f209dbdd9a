<!-- account/templates/account/detail.html -->
{% extends 'layouts/main_layout.html' %}
{% load django_bootstrap5 %}

{% block title %}회원정보 조회 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">회원정보 조회</h1>
                <p class="lead fw-normal text-muted mb-0">{{ user.username }}님의 회원정보를 확인하고 수정할 수 있습니다</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <!-- User info card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-person-circle me-2"></i>회원 정보
                            </h4>
                        </div>

                        <div class="card-body p-4">
                            <div class="row">
                                <!-- User Avatar -->
                                <div class="col-md-4 text-center mb-4">
                                    <div class="bg-primary rounded-circle d-inline-flex align-items-center justify-content-center"
                                         style="width: 120px; height: 120px;">
                                        <i class="bi bi-person-fill text-white" style="font-size: 3rem;"></i>
                                    </div>
                                    <h5 class="mt-3 mb-0">{{ user.name|default:user.username }}</h5>
                                    <small class="text-muted">@{{ user.username }}</small>
                                </div>

                                <!-- User Details -->
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">
                                            <i class="bi bi-person me-2"></i>사용자명
                                        </label>
                                        <div class="form-control-plaintext bg-light rounded p-2">
                                            {{ user.username }}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">
                                            <i class="bi bi-card-text me-2"></i>이름
                                        </label>
                                        <div class="form-control-plaintext bg-light rounded p-2">
                                            {{ user.name|default:"미입력" }}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">
                                            <i class="bi bi-envelope me-2"></i>이메일
                                        </label>
                                        <div class="form-control-plaintext bg-light rounded p-2">
                                            {{ user.email|default:"미입력" }}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">
                                            <i class="bi bi-calendar me-2"></i>생년월일
                                        </label>
                                        <div class="form-control-plaintext bg-light rounded p-2">
                                            {{ user.birthday|date:"Y년 m월 d일"|default:"미입력" }}
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label fw-bold text-primary">
                                            <i class="bi bi-calendar-plus me-2"></i>가입일
                                        </label>
                                        <div class="form-control-plaintext bg-light rounded p-2">
                                            {{ user.date_joined|date:"Y년 m월 d일 H:i" }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Action buttons -->
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                <a href="{% url 'account:update' %}" class="btn btn-primary me-md-2">
                                    <i class="bi bi-pencil-square me-2"></i>정보 수정
                                </a>
                                <a href="{% url 'account:password_change' %}" class="btn btn-outline-secondary">
                                    <i class="bi bi-key me-2"></i>비밀번호 변경
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Statistics card -->
                    <div class="mt-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body p-4">
                                <h5 class="fw-bold mb-3">
                                    <i class="bi bi-bar-chart me-2"></i>활동 통계
                                </h5>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="fw-bold text-primary h4">{{ user.vote_set.count }}</div>
                                        <small class="text-muted">참여한 투표</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fw-bold text-success h4">0</div>
                                        <small class="text-muted">생성한 설문</small>
                                    </div>
                                    <div class="col-4">
                                        <div class="fw-bold text-info h4">
                                            {% if user.vote_set.exists %}
                                                {{ user.vote_set.first.voted_at|timesince }}전
                                            {% else %}
                                                -
                                            {% endif %}
                                        </div>
                                        <small class="text-muted">최근 활동</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .form-control-plaintext {
            border: 1px solid #e9ecef;
            font-weight: 500;
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock contents %}
