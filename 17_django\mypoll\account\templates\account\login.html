<!-- account/templates/account/login.html -->
{% extends 'layouts/main_layout.html' %}
{% load django_bootstrap5 %}

{% comment %}
    django_bootstrap5 사용. django template에서 bootstrap을 적용할 수 있는 태그들을 제공하는 lib
{% endcomment %}

{% block title %}로그인 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">로그인</h1>
                <p class="lead fw-normal text-muted mb-0">Modern Survey에 로그인하여 설문조사에 참여해보세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-5 col-xl-4">
                    <!-- Login form card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-box-arrow-in-right me-2"></i>로그인
                            </h4>
                        </div>
                        
                        <div class="card-body p-4">
                            <form method="post">
                                {% csrf_token %}
                                
                                <!-- Bootstrap Form 사용 - floating labels -->
                                {% bootstrap_form form layout='floating' %}
                                
                                <!-- Action buttons -->
                                <div class="d-grid gap-2 mt-4">
                                    {% bootstrap_button button_type="submit" content="<i class='bi bi-box-arrow-in-right me-2'></i>로그인" button_class="btn-primary" %}
                                </div>
                                
                                <!-- Links -->
                                <div class="text-center mt-3">
                                    <p class="mb-0">
                                        <small class="text-muted">
                                            계정이 없으신가요? 
                                            <a href="{% url 'account:create' %}" class="text-decoration-none">회원가입</a>
                                        </small>
                                    </p>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Help section -->
                    <div class="mt-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body p-4">
                                <h5 class="fw-bold mb-3">
                                    <i class="bi bi-info-circle me-2"></i>로그인 안내
                                </h5>
                                <ul class="mb-0">
                                    <li class="mb-2">회원가입 시 등록한 사용자명과 비밀번호를 입력하세요</li>
                                    <li class="mb-2">로그인 후 설문조사 참여가 가능합니다</li>
                                    <li>계정이 없으시면 회원가입을 먼저 진행해주세요</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Floating labels 스타일 개선 */
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: #0d6efd;
            transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
        }
        
        .form-floating > .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }
        
        /* 오류 메시지 스타일 */
        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
        }
        
        /* 카드 애니메이션 */
        .card {
            animation: fadeInUp 0.6s ease-out;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 링크 스타일 */
        a {
            color: #0d6efd;
            transition: color 0.3s ease;
        }
        
        a:hover {
            color: #0a58ca;
        }
    </style>
{% endblock contents %}
