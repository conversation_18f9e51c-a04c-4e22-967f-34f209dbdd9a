<!-- account/templates/account/create.html -->
{% extends 'layouts/main_layout.html' %}
{% load django_bootstrap5 %}

{% comment %}
    django_bootstrap5 사용. django template에서 bootstrap을 적용할 수 있는 태그들을 제공하는 lib
    `pip install django_bootstrap5`
    `settings.py`에 `INSTALLED_APPS`에 `django_bootstrap5` 추가
{% endcomment %}

{% block title %}회원가입 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">회원가입</h1>
                <p class="lead fw-normal text-muted mb-0">Modern Survey에 가입하여 설문조사에 참여해보세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-6 col-xl-5">
                    <!-- Registration form card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-person-plus me-2"></i>회원 정보 입력
                            </h4>
                        </div>

                        <div class="card-body p-4">
                            <form method="post">
                                {% csrf_token %}

                                <!-- Bootstrap Form 사용 - 모든 필드를 자동으로 렌더링 -->
                                {% bootstrap_form form layout='floating' %}

                                <!-- Action buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                    <a href="{% url 'home' %}" class="btn btn-outline-secondary me-md-2">
                                        <i class="bi bi-arrow-left me-2"></i>취소
                                    </a>
                                    {% bootstrap_button button_type="reset" content="<i class='bi bi-arrow-clockwise me-2'></i>초기화" button_class="btn-outline-primary me-md-2" %}
                                    {% bootstrap_button button_type="submit" content="<i class='bi bi-check-circle me-2'></i>가입하기" button_class="btn-primary" %}
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Help section -->
                    <div class="mt-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body p-4">
                                <h5 class="fw-bold mb-3">
                                    <i class="bi bi-info-circle me-2"></i>회원가입 안내
                                </h5>
                                <ul class="mb-0">
                                    <li class="mb-2">사용자명은 로그인 시 사용됩니다</li>
                                    <li class="mb-2">이메일은 중복될 수 없습니다</li>
                                    <li class="mb-2">비밀번호는 8자 이상이어야 합니다</li>
                                    <li>생년월일은 선택사항입니다</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        /* Floating labels 스타일 개선 */
        .form-floating > .form-control:focus ~ label,
        .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: #0d6efd;
            transform: scale(.85) translateY(-0.5rem) translateX(0.15rem);
        }

        /* Floating labels 스타일 개선 */
        .form-floating > .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        /* 오류 메시지 스타일 */
        .invalid-feedback {
            display: block;
            font-size: 0.875rem;
            color: #dc3545;
        }

        /* 도움말 텍스트 스타일 */
        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
        }

        /* 카드 애니메이션 */
        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock contents %}