<!-- account/templates/account/create.html -->
{% comment %}
    django_bootstrap5 사용. django template에서 bootstrap을 적용할 수 있는 태그들을 제공하는 lib
    `pip install django_bootstrap5`
    `settings.py`에 `INSTALLED_APPS`에 `django_bootstrap5` 추가
{% endcomment %}

{% extends 'layouts/main_layout.html' %}
{% load django_bootstrap5 %}
{% block title %}회원가입 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">회원가입</h1>
                <p class="lead fw-normal text-muted mb-0">Modern Survey에 가입하여 설문조사에 참여해보세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-6 col-xl-5">
                    <!-- Registration form card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-person-plus me-2"></i>회원 정보 입력
                            </h4>
                        </div>

                        <div class="card-body p-4">
                            <form method="post">
                                {% csrf_token %}
                                {% bootstrap_form form %}

                                <!-- Username field -->
                                <div class="mb-3">
                                    <label for="{{ form.username.id_for_label }}" class="form-label fw-bold">
                                        <i class="bi bi-person me-2"></i>사용자명
                                    </label>
                                    {{ form.username }}
                                    {% if form.username.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.username.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">로그인 시 사용할 사용자명을 입력하세요.</div>
                                </div>

                                <!-- Name field -->
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label fw-bold">
                                        <i class="bi bi-card-text me-2"></i>이름
                                    </label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.name.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Email field -->
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label fw-bold">
                                        <i class="bi bi-envelope me-2"></i>이메일
                                    </label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.email.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Birthday field -->
                                <div class="mb-3">
                                    <label for="{{ form.birthday.id_for_label }}" class="form-label fw-bold">
                                        <i class="bi bi-calendar me-2"></i>생년월일
                                    </label>
                                    {{ form.birthday }}
                                    {% if form.birthday.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.birthday.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <div class="form-text">선택사항입니다.</div>
                                </div>

                                <!-- Password1 field -->
                                <div class="mb-3">
                                    <label for="{{ form.password1.id_for_label }}" class="form-label fw-bold">
                                        <i class="bi bi-lock me-2"></i>비밀번호
                                    </label>
                                    {{ form.password1 }}
                                    {% if form.password1.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.password1.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Password2 field -->
                                <div class="mb-4">
                                    <label for="{{ form.password2.id_for_label }}" class="form-label fw-bold">
                                        <i class="bi bi-lock-fill me-2"></i>비밀번호 확인
                                    </label>
                                    {{ form.password2 }}
                                    {% if form.password2.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.password2.errors %}
                                                <div>{{ error }}</div>
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Action buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'home' %}" class="btn btn-outline-secondary me-md-2">
                                        <i class="bi bi-arrow-left me-2"></i>취소
                                    </a>
                                    <button type="reset" class="btn btn-outline-primary me-md-2">
                                        <i class="bi bi-arrow-clockwise me-2"></i>초기화
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>가입하기
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Help section -->
                    <div class="mt-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body p-4">
                                <h5 class="fw-bold mb-3">
                                    <i class="bi bi-info-circle me-2"></i>회원가입 안내
                                </h5>
                                <ul class="mb-0">
                                    <li class="mb-2">사용자명은 로그인 시 사용됩니다</li>
                                    <li class="mb-2">이메일은 중복될 수 없습니다</li>
                                    <li class="mb-2">비밀번호는 8자 이상이어야 합니다</li>
                                    <li>생년월일은 선택사항입니다</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .form-control {
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
            padding: 0.5rem 0.75rem;
        }

        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        .text-danger {
            font-size: 0.875rem;
        }

        .form-text {
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
{% endblock contents %}