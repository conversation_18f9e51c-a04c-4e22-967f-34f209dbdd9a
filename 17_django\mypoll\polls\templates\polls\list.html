{% extends 'layouts/main_layout.html' %}

{% block title %}설문 목록{% endblock %}

{% block contents %}
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-5 fw-bold text-primary">
            <i class="bi bi-list-ul me-3"></i>설문조사 목록
        </h1>
        <a href="{% url 'polls:vote_create' %}" class="btn btn-success btn-custom">
            <i class="bi bi-plus-circle me-2"></i>새 설문조사 만들기
        </a>
    </div>

    {% for question in question_list %}
        <div class="card mb-3 shadow-sm border-0 hover-card">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="card-title mb-2">
                            <span class="badge bg-primary me-2">{{ question.pk }}</span>
                            {{ question.question_text }}
                        </h5>
                        <p class="card-text text-muted mb-0">
                            <i class="bi bi-calendar3 me-1"></i>
                            등록일: {{ question.pub_date|date:"Y년 m월 d일 H:i" }}
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <a href="{% url 'polls:vote_form' question.pk %}" class="btn btn-outline-primary btn-custom">
                            <i class="bi bi-hand-index me-2"></i>투표하기
                        </a>
                    </div>
                </div>
            </div>
        </div>
    {% empty %}
        <div class="text-center py-5">
            <i class="bi bi-inbox display-1 text-muted mb-3"></i>
            <h3 class="text-muted">등록된 설문조사가 없습니다</h3>
            <p class="text-muted mb-4">첫 번째 설문조사를 만들어보세요!</p>
            <a href="{% url 'polls:vote_create' %}" class="btn btn-primary btn-custom">
                <i class="bi bi-plus-circle me-2"></i>설문조사 만들기
            </a>
        </div>
    {% endfor %}

    <style>
        .hover-card {
            transition: all 0.3s ease;
        }
        .hover-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
        }
    </style>
{% endblock contents %}