{% extends 'layouts/main_layout.html' %}

{% block title %}설문조사 목록 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">설문조사 목록</h1>
                <p class="lead fw-normal text-muted mb-0">참여 가능한 설문조사를 확인하고 투표해보세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <!-- Action buttons -->
            <div class="row gx-4 gx-lg-5 mb-5">
                <div class="col-lg-8 col-xl-6">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="fw-bolder mb-0">현재 진행 중인 설문</h2>
                        <a href="{% url 'polls:vote_create' %}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>새 설문 만들기
                        </a>
                    </div>
                </div>
            </div>

            <!-- Survey list -->
            <div class="row gx-4 gx-lg-5">
                {% for question in question_list %}
                    <div class="col-lg-6 mb-5">
                        <div class="card h-100 shadow border-0">
                            <div class="card-body p-4">
                                <div class="badge bg-primary bg-gradient rounded-pill mb-2">설문 #{{ question.pk }}</div>
                                <a class="text-decoration-none link-dark stretched-link" href="{% url 'polls:vote_form' question.pk %}">
                                    <h5 class="card-title mb-3">{{ question.question_text }}</h5>
                                </a>
                                <p class="card-text mb-0">이 설문조사에 참여하여 여러분의 소중한 의견을 들려주세요.</p>
                            </div>
                            <div class="card-footer p-4 pt-0 bg-transparent border-top-0">
                                <div class="d-flex align-items-end justify-content-between">
                                    <div class="d-flex align-items-center">
                                        <div class="small">
                                            <div class="fw-bold">설문 관리자</div>
                                            <div class="text-muted">{{ question.pub_date|date:"Y년 m월 d일 H:i" }}</div>
                                        </div>
                                    </div>
                                    <div class="small text-muted">
                                        <i class="bi bi-people me-1"></i>
                                        {% with total_votes=0 %}
                                            {% for choice in question.choice_set.all %}
                                                {% with total_votes=total_votes|add:choice.votes %}{% endwith %}
                                            {% endfor %}
                                            {{ total_votes }}명 참여
                                        {% endwith %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <!-- Empty state -->
                    <div class="col-12">
                        <div class="text-center py-5">
                            <div class="feature bg-light text-muted rounded-3 mb-4 mx-auto" style="width: 8rem; height: 8rem;">
                                <i class="bi bi-inbox" style="font-size: 3rem;"></i>
                            </div>
                            <h2 class="fw-bolder">아직 등록된 설문조사가 없습니다</h2>
                            <p class="lead fw-normal text-muted mb-4">첫 번째 설문조사를 만들어 의견 수집을 시작해보세요!</p>
                            <a class="btn btn-primary btn-lg" href="{% url 'polls:vote_create' %}">
                                <i class="bi bi-plus-circle me-2"></i>설문조사 만들기
                            </a>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
                <div class="row">
                    <div class="col-12">
                        <nav aria-label="설문조사 목록 페이지네이션">
                            <ul class="pagination justify-content-center">
                                <!-- Previous page -->
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1" aria-label="첫 페이지">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="이전 페이지">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&laquo;&laquo;</span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link">&laquo;</span>
                                    </li>
                                {% endif %}

                                <!-- Page numbers -->
                                {% for num in page_obj.paginator.page_range %}
                                    {% if page_obj.number == num %}
                                        <li class="page-item active" aria-current="page">
                                            <span class="page-link">{{ num }}</span>
                                        </li>
                                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                        <li class="page-item">
                                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                        </li>
                                    {% endif %}
                                {% endfor %}

                                <!-- Next page -->
                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="다음 페이지">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="마지막 페이지">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">&raquo;</span>
                                    </li>
                                    <li class="page-item disabled">
                                        <span class="page-link">&raquo;&raquo;</span>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>

                        <!-- Page info -->
                        <div class="text-center text-muted mt-3">
                            <small>
                                {{ page_obj.start_index }}~{{ page_obj.end_index }}번 설문조사
                                (전체 {{ page_obj.paginator.count }}개 중 {{ page_obj.number }}페이지)
                            </small>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </section>

    <!-- Call to action -->
    {% if page_obj.object_list %}
    <section class="py-5 bg-light">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h2 class="fw-bolder">더 많은 의견이 필요하신가요?</h2>
                <p class="lead fw-normal text-muted mb-4">새로운 설문조사를 만들어 더 많은 사람들의 의견을 수집해보세요.</p>
                <a class="btn btn-primary btn-lg" href="{% url 'polls:vote_create' %}">설문조사 만들기</a>
            </div>
        </div>
    </section>
    {% endif %}

    <style>
        .feature {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .card {
            transition: transform 0.15s ease-in-out;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        /* Pagination styles */
        .pagination {
            margin-bottom: 0;
        }

        .page-link {
            color: #0d6efd;
            border-color: #dee2e6;
            padding: 0.5rem 0.75rem;
        }

        .page-link:hover {
            color: #0a58ca;
            background-color: #e9ecef;
            border-color: #dee2e6;
        }

        .page-item.active .page-link {
            background-color: #0d6efd;
            border-color: #0d6efd;
            color: white;
        }

        .page-item.disabled .page-link {
            color: #6c757d;
            background-color: #fff;
            border-color: #dee2e6;
        }
    </style>
{% endblock contents %}