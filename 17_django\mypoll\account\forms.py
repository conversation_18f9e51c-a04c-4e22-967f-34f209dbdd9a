# account/forms.py - Form 클래스 정의
## Form이나 ModelForm클래스들을 정의하는 모듈
## 보통 Form은 등록/수정 폼 각각 하나씩 정의.

### User 등록폼, user 수정폼 ==> ModelForm
from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import User

# ModelForm: ModelForm을 상속, Form forms.Form을 상속

# UserCreationForm에 정의된 Form필드 : username, password1, password2
# CustomUserCreationForm : UserCreationForm의 form필드 + name, email, birthday
class CustomUserCreationForm(UserCreationForm):

    class Meta(UserCreationForm.Meta):
        model = User # User Model의 model Field를 이용해서 Form field를 구성
        # fields = "__all__" # 모델의 모든 field들을 이용해서 구성
        fields = ["username","password1","password2", "name", "email", "birthday"] #특정 field들을 선택해서 구성
        # exclude = ["password1", "password2"] # 제외할 field들을 지정해서 구성

        widgets = {
            'username': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '사용자명을 입력하세요'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '이름을 입력하세요'}),
            'email': forms.EmailInput(attrs={'class': 'form-control', 'placeholder': '<EMAIL>'}),
            'birthday': forms.DateInput(attrs={'class': 'form-control', 'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 비밀번호 필드에도 Bootstrap 클래스 추가
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '비밀번호를 입력하세요'
        })
        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '비밀번호를 다시 입력하세요'
        })


