# account/forms.py - Form 클래스 정의
## Form이나 ModelForm클래스들을 정의하는 모듈
## 보통 Form은 등록/수정 폼 각각 하나씩 정의.

### User 등록폼, user 수정폼 ==> ModelForm
from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm
from .models import User

# ModelForm: ModelForm을 상속, Form forms.Form을 상속

# UserCreationForm에 정의된 Form필드 : username, password1, password2
# CustomUserCreationForm : UserCreationForm의 form필드 + name, email, birthday
class CustomUserCreationForm(UserCreationForm):

    class Meta(UserCreationForm.Meta):
        model = User # User Model의 model Field를 이용해서 Form field를 구성
        fields = "__all__" # 모델의 모든 field들을 이용해서 구성
        # field = ["username", "name", "email", "birthday"] #특정 field들을 선택해서 구성
        # exclude = ["password1", "password2"] # 제외할 field들을 지정해서 구성
        

