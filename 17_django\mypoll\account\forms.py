# account/forms.py - Form 클래스 정의
## Form이나 ModelForm클래스들을 정의하는 모듈
## 보통 Form은 등록/수정 폼 각각 하나씩 정의.

### User 등록폼, user 수정폼 ==> ModelForm
from django import forms
from django.contrib.auth.forms import UserCreationForm, UserChangeForm, AuthenticationForm
from .models import User

# ModelForm: ModelForm을 상속, Form forms.Form을 상속

# UserCreationForm에 정의된 Form필드 : username, password1, password2
# CustomUserCreationForm : UserCreationForm의 form필드 + name, email, birthday
class CustomUserCreationForm(UserCreationForm):

    class Meta(UserCreationForm.Meta):
        model = User # User Model의 model Field를 이용해서 Form field를 구성
        # fields = "__all__" # 모델의 모든 field들을 이용해서 구성
        fields = ["username","password1","password2", "name", "email", "birthday"] #특정 field들을 선택해서 구성
        # exclude = ["password1", "password2"] # 제외할 field들을 지정해서 구성

        widgets = {
            'username': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '사용자명'
            }),
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '이름'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': '이메일 주소'
            }),
            'birthday': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'placeholder': '생년월일'
            }),
        }

        labels = {
            'username': '사용자명',
            'name': '이름',
            'email': '이메일',
            'birthday': '생년월일 (선택사항)',
        }

        help_texts = {
            'username': '로그인 시 사용할 사용자명을 입력하세요.',
            'email': '유효한 이메일 주소를 입력하세요.',
            'birthday': '선택사항입니다.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 비밀번호 필드에도 Bootstrap 클래스와 라벨 추가
        self.fields['password1'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '비밀번호'
        })
        self.fields['password1'].label = '비밀번호'

        self.fields['password2'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '비밀번호 확인'
        })
        self.fields['password2'].label = '비밀번호 확인'


# 로그인 폼 커스터마이징
class CustomAuthenticationForm(AuthenticationForm):

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # 사용자명 필드 커스터마이징
        self.fields['username'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '사용자명'
        })
        self.fields['username'].label = '사용자명'

        # 비밀번호 필드 커스터마이징
        self.fields['password'].widget.attrs.update({
            'class': 'form-control',
            'placeholder': '비밀번호'
        })
        self.fields['password'].label = '비밀번호'


# 회원정보 수정 폼 - modelForm
class CustomUserChangeForm(UserChangeForm):

    class Meta(UserChangeForm.Meta):
        model = User
        fields = ["name", "email", "birthday"]
        widget = {
            "birthday": forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date',
                'placeholder': '생년월일'
            }),
        }
    
    def clean_name(self):
        name = self.cleaned_data.get("name")
        if name < 2:
            raise forms.ValidationError("이름은 2글자 이상이어야 합니다.")
        return name


