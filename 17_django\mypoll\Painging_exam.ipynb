# django shell 환경설정
import os
import django
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings'
os.environ['DJANGO_ALLOW_ASYNC_UNSAFE'] = 'true'

django.setup()

txt = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
dataset = list(txt)
# dataset

from django.core.paginator import Paginator

pn = Paginator(dataset,5)

pn.num_pages, pn.count, pn.page_range









































