{"cells": [{"cell_type": "code", "execution_count": 10, "id": "bc6700eb", "metadata": {"ExecuteTime": {"end_time": "2022-01-03T08:13:10.849537Z", "start_time": "2022-01-03T08:13:10.308822Z"}}, "outputs": [], "source": ["# django shell 환경설정\n", "import os\n", "import django\n", "os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings'\n", "os.environ['DJANG<PERSON>_ALLOW_ASYNC_UNSAFE'] = 'true'\n", "\n", "django.setup()"]}, {"cell_type": "markdown", "id": "3adbb7a6", "metadata": {}, "source": ["# 페이징 처리\n", "-  대량의 데이터를 여러 페이지로 나눠서 출력하는 것.\n", "-  Django에서는 Paginator와 Page 클래스를 통해 처리한다.\n", "  \n", "## Paginator 클래스\n", "- 전체 페이징 처리를 관리하는 클래스\n", "- 전체 데이터관련 정보, 각 페이지당 보여줄 데이터의 정보 등을 제공\n", "\n", "## Page 클래스\n", "- 한페이지에대한 데이터를 관리\n", "- Paginator를 통해서 생성.\n", "    - `Pagenator객체.page(페이지 번호)`\n", "- iterable 타입. 페이지에 속한 데이터들을 제공\n", "- Page객체.object_list 속성: 페이지가 가진 데이터들을 List로 반환"]}, {"cell_type": "code", "execution_count": 3, "id": "733b5ff6", "metadata": {}, "outputs": [], "source": ["txt = \"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ\"\n", "dataset = list(txt)\n", "# dataset"]}, {"cell_type": "code", "execution_count": 11, "id": "c4114389", "metadata": {}, "outputs": [], "source": ["from django.core.paginator import Paginator\n", "\n", "pn = Paginator(dataset,5)"]}, {"cell_type": "code", "execution_count": null, "id": "2767dd5b", "metadata": {}, "outputs": [{"data": {"text/plain": ["(13, 62, range(1, 14))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["pn.num_pages, pn.count, pn.page_range"]}, {"cell_type": "code", "execution_count": null, "id": "255bdd53", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "e6b65836", "metadata": {}, "source": ["## 이전/다음 페이지가 있는지 \n", "- `Page객체.has_previous()` / `Page객체.has_next()`\n", "- 1페이지: 이전페이지? X, 다음페이지? O\n", "- 중간 페이지: 이전페이지? O, 다음페이지? O\n", "- 마지막 페이지: 이전? O, 다음페이지? X"]}, {"cell_type": "code", "execution_count": null, "id": "bdac974d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a9000f39", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "246cf066", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "97d01906", "metadata": {}, "source": ["## 이전/다음페이지 번호 조회\n", "- **Page객체.number:** 현재 페이지 번호\n", "- **Page객체.previous_page_number():** 이전페이지 번호 조회\n", "- **page객체.next_page_number():** 다음페이지 번호 조회"]}, {"cell_type": "code", "execution_count": null, "id": "1092de08", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "eb4bc2e9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4dd17739", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "9053ab91", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "0d301a75", "metadata": {}, "source": ["## 각 페이지별 데이터를 출력(조회)"]}, {"cell_type": "code", "execution_count": null, "id": "abfdda57", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e84ccb77", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3751597d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "fd01b375", "metadata": {}, "source": ["# 현재 페이지(요청페이지)가 속한 page 그룹의 (page_range)에서의 시작 index와 끝 index를 조회"]}, {"cell_type": "code", "execution_count": 5, "id": "d73d22c9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Page 1 of 13>\n", "<Page 2 of 13>\n", "<Page 3 of 13>\n", "<Page 4 of 13>\n", "<Page 5 of 13>\n", "<Page 6 of 13>\n", "<Page 7 of 13>\n", "<Page 8 of 13>\n", "<Page 9 of 13>\n", "<Page 10 of 13>\n", "<Page 11 of 13>\n", "<Page 12 of 13>\n", "<Page 13 of 13>\n"]}], "source": ["for p in pn.page_range:\n", "    print(pn.get_page(p))"]}, {"cell_type": "code", "execution_count": 6, "id": "56aef734", "metadata": {}, "outputs": [], "source": ["pn = Paginator(dataset, 5)"]}, {"cell_type": "code", "execution_count": null, "id": "835debde", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d8ab6a97", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1ced076c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "46d182a4", "metadata": {}, "source": ["## Question, Choice Dummy 데이터 추가"]}, {"cell_type": "code", "execution_count": 12, "id": "0b08c85b", "metadata": {}, "outputs": [], "source": ["from polls.models import Question, Choice\n"]}, {"cell_type": "code", "execution_count": null, "id": "96156a6f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["좋아하는 색깔은?\n", "\t빨강\n", "\t파랑\n", "\t노랑\n", "\t초록\n", "사는 지역은?\n", "\t서울\n", "\t경기\n", "좋아하는 음료는?\n", "\t보드카\n", "\t물\n", "배우고 싶은 언어는 무엇입니까?\n", "\t미국\n", "\t일본\n", "\t영국\n", "새로운질문\n", "\t답변1\n", "\t답변2\n", "\t답변3\n", "여섯번째 질문\n", "\t가\n", "\t나\n", "\t다\n", "하늘은 무슨색일까\n", "\t파란색\n", "\t하늘색\n", "\t남색\n", "잡상인은 누굴까요\n", "\t신문\n", "\t양말\n", "\t고구마\n", "부트스트랩 새로운 템플렛\n", "\t모던\n", "\t크리에이티브\n", "새로운 페이지는 언제인가\n", "\t1\n", "하나더 추가한다\n", "\t11\n", "오늘 점심 메뉴는?\n", "\t구내식당\n", "\t냉면\n", "\t국밥\n", "오늘 저녁은?\n", "\t집밥\n", "\t김치찌개\n", "\t된장찌개\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7865b734", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a72edbd5", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "50c56984", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "lang_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": true, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {}, "toc_section_display": true, "toc_window_display": false}, "varInspector": {"cols": {"lenName": 16, "lenType": 16, "lenVar": 40}, "kernels_config": {"python": {"delete_cmd_postfix": "", "delete_cmd_prefix": "del ", "library": "var_list.py", "varRefreshCmd": "print(var_dic_list())"}, "r": {"delete_cmd_postfix": ") ", "delete_cmd_prefix": "rm(", "library": "var_list.r", "varRefreshCmd": "cat(var_dic_list()) "}}, "types_to_exclude": ["module", "function", "builtin_function_or_method", "instance", "_Feature"], "window_display": false}}, "nbformat": 4, "nbformat_minor": 5}