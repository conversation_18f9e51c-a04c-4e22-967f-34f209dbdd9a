{"cells": [{"cell_type": "code", "execution_count": 2, "id": "bb3c23fd", "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'django_bootstrap5'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 10\u001b[39m\n\u001b[32m      7\u001b[39m os.environ[\u001b[33m'\u001b[39m\u001b[33mDJANGO_SETTINGS_MODULE\u001b[39m\u001b[33m'\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33mconfig.settings\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      8\u001b[39m os.environ[\u001b[33m'\u001b[39m\u001b[33mDJANGO_ALLOW_ASYNC_UNSAFE\u001b[39m\u001b[33m'\u001b[39m] = \u001b[33m\"\u001b[39m\u001b[33mtrue\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m10\u001b[39m \u001b[43mdjango\u001b[49m\u001b[43m.\u001b[49m\u001b[43msetup\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\Lib\\site-packages\\django\\__init__.py:24\u001b[39m, in \u001b[36msetup\u001b[39m\u001b[34m(set_prefix)\u001b[39m\n\u001b[32m     20\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m set_prefix:\n\u001b[32m     21\u001b[39m     set_script_prefix(\n\u001b[32m     22\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m/\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m settings.FORCE_SCRIPT_NAME \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m settings.FORCE_SCRIPT_NAME\n\u001b[32m     23\u001b[39m     )\n\u001b[32m---> \u001b[39m\u001b[32m24\u001b[39m \u001b[43mapps\u001b[49m\u001b[43m.\u001b[49m\u001b[43mpopulate\u001b[49m\u001b[43m(\u001b[49m\u001b[43msettings\u001b[49m\u001b[43m.\u001b[49m\u001b[43mINSTALLED_APPS\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\Lib\\site-packages\\django\\apps\\registry.py:91\u001b[39m, in \u001b[36mApps.populate\u001b[39m\u001b[34m(self, installed_apps)\u001b[39m\n\u001b[32m     89\u001b[39m     app_config = entry\n\u001b[32m     90\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m91\u001b[39m     app_config = \u001b[43mAppConfig\u001b[49m\u001b[43m.\u001b[49m\u001b[43mcreate\u001b[49m\u001b[43m(\u001b[49m\u001b[43mentry\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     92\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m app_config.label \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28mself\u001b[39m.app_configs:\n\u001b[32m     93\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m ImproperlyConfigured(\n\u001b[32m     94\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mApplication labels aren\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt unique, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m     95\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mduplicates: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[33m\"\u001b[39m % app_config.label\n\u001b[32m     96\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\Lib\\site-packages\\django\\apps\\config.py:193\u001b[39m, in \u001b[36mAppConfig.create\u001b[39m\u001b[34m(cls, entry)\u001b[39m\n\u001b[32m    190\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mImportError\u001b[39;00m(msg)\n\u001b[32m    191\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    192\u001b[39m         \u001b[38;5;66;03m# Re-trigger the module import exception.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m193\u001b[39m         \u001b[43mimport_module\u001b[49m\u001b[43m(\u001b[49m\u001b[43mentry\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    195\u001b[39m \u001b[38;5;66;03m# Check for obvious errors. (This check prevents duck typing, but\u001b[39;00m\n\u001b[32m    196\u001b[39m \u001b[38;5;66;03m# it could be removed if it became a problem in practice.)\u001b[39;00m\n\u001b[32m    197\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28missubclass\u001b[39m(app_config_class, AppConfig):\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\Lib\\importlib\\__init__.py:90\u001b[39m, in \u001b[36mimport_module\u001b[39m\u001b[34m(name, package)\u001b[39m\n\u001b[32m     88\u001b[39m             \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[32m     89\u001b[39m         level += \u001b[32m1\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m90\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_bootstrap\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_gcd_import\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m[\u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m:\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpackage\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen importlib._bootstrap>:1387\u001b[39m, in \u001b[36m_gcd_import\u001b[39m\u001b[34m(name, package, level)\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen importlib._bootstrap>:1360\u001b[39m, in \u001b[36m_find_and_load\u001b[39m\u001b[34m(name, import_)\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m<frozen importlib._bootstrap>:1324\u001b[39m, in \u001b[36m_find_and_load_unlocked\u001b[39m\u001b[34m(name, import_)\u001b[39m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'django_bootstrap5'"]}], "source": ["# mypoll/ORM_Exam.ipynb\n", "\n", "## <PERSON><PERSON>ter Lab에서 Django shell을 실행하기 위한 설정\n", "import os\n", "import django\n", "# 환경변수로 config/settings.py의 위치를 설정\n", "os.environ['DJANGO_SETTINGS_MODULE'] = \"config.settings\"\n", "os.environ['DJAN<PERSON><PERSON>_ALLOW_ASYNC_UNSAFE'] = \"true\"\n", "\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 94, "id": "5eaa3be5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [<Question: 1. 좋아하는 색깔은 무엇입니까?>, <Question: 2. 좋아하는 동물은 무엇입니까?>, <Question: 3. 싫어하는 색깔은 고르세요.>, <Question: 4. 여행으로 가고 싶은 나라를 선택해주세요.>, <Question: 5. 배우고 싶은 언어는 무엇입니까?>]>"]}, "execution_count": 94, "metadata": {}, "output_type": "execute_result"}], "source": ["# 조회 테스트\n", "from polls.models import Question, Choice\n", "\n", "q = Question.objects.all()\n", "q"]}, {"cell_type": "code", "execution_count": null, "id": "76aa61f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 96, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "markdown", "id": "56ed7a08", "metadata": {}, "source": ["# 조회\n", "- ModelClass.objects -> Model Manager를 반환.\n", "- ModelManager: SQL작업을 할 수있는 메소드들을 제공하는 객체\n", "\n", "## 조회메소드\n", "- `all()`: 전체 조회\n", "- `filter()`, `exclude()`: 조건으로 조회(where절)\n", "- `get()`: 조회결과가 하나인 조건으로 조회(PK로 조회)\n", "\n", "## 조회결과\n", "- `QuerySet` 객체: 조회결과가 여러개일때 QuerySet에 모아서 반환.\n", "  - 조회결과를 바탕으로 추가 DB 작업을 진행할 수 있는 메소드들을 제공.\n", "  - 개별 데이터는 Model 객체에 담아서 반환.\n", "- Model 객체: 조회결과가 하나(`get()`) 일 때 "]}, {"cell_type": "code", "execution_count": 3, "id": "b836bace", "metadata": {}, "outputs": [], "source": ["from polls.models import Question, Choice"]}, {"cell_type": "code", "execution_count": 4, "id": "102b4553", "metadata": {}, "outputs": [{"data": {"text/plain": ["django.db.models.manager.Manager"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["model_manager = Question.objects\n", "type(model_manager)"]}, {"cell_type": "code", "execution_count": 7, "id": "63b472ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["조회한 데이터개수: 4\n", "all()로 실행된 SQL문을 조회\n", "SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\"\n"]}], "source": ["result = model_manager.all()\n", "print(\"조회한 데이터개수:\", len(result))\n", "print(\"all()로 실행된 SQL문을 조회\")\n", "print(result.query)"]}, {"cell_type": "code", "execution_count": 10, "id": "9ec1e39b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'django.db.models.query.QuerySet'>\n"]}, {"data": {"text/plain": ["<QuerySet [<Question: 1. 좋아하는 색깔은 무엇입니까?>, <Question: 2. 좋아하는 동물은 무엇입니까?>, <Question: 3. 싫어하는 색깔은 고르세요.>, <Question: 4. 여행가고 싶은 나라는?>]>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["print(type(result))\n", "result"]}, {"cell_type": "code", "execution_count": 13, "id": "5a420166", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 좋아하는 색깔은 무엇입니까? 2025-07-07 05:17:02.657964+00:00\n", "2. 좋아하는 동물은 무엇입니까? 2025-07-07 05:17:27.441139+00:00\n", "3. 싫어하는 색깔은 고르세요. 2025-07-07 05:17:41.937410+00:00\n", "4. 여행가고 싶은 나라는? 2025-07-07 06:15:03.568627+00:00\n"]}], "source": ["# QuerySet -> Iterable\n", "for q in result:\n", "    # print(type(q))\n", "    print(q, q.pub_date)"]}, {"cell_type": "code", "execution_count": 19, "id": "1c40dd0b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2 2\n", "좋아하는 동물은 무엇입니까? 2025-07-07 05:17:27.441139+00:00\n", "<class 'int'> <class 'str'> <class 'datetime.datetime'>\n"]}], "source": ["# QuerySet -> Subscriptable\n", "q = result[1]\n", "print(q.id, q.pk)\n", "print(q.question_text, q.pub_date)\n", "print(type(q.pk), type(q.question_text), type(q.pub_date))"]}, {"cell_type": "code", "execution_count": 20, "id": "399fd8d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 4)"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# QuerySet - 첫번째, 마지막 index값 조회\n", "q_s = result.first()\n", "q_e = result.last()\n", "q_s.pk, q_e.pk"]}, {"cell_type": "code", "execution_count": 24, "id": "0ac0cc15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n"]}, {"data": {"text/plain": ["[<Question: 1. 좋아하는 색깔은 무엇입니까?>,\n", " <Question: 2. 좋아하는 동물은 무엇입니까?>,\n", " <Question: 3. 싫어하는 색깔은 고르세요.>]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["# slicing -> 결과: list\n", "s_result = result[:3]\n", "print(type(s_result))\n", "s_result"]}, {"cell_type": "code", "execution_count": 27, "id": "536c262e", "metadata": {}, "outputs": [], "source": ["# 음수 indexing은 지원하지 않는다.\n", "# result[-2] "]}, {"cell_type": "code", "execution_count": 32, "id": "a2ccac0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [<Question: 1. 좋아하는 색깔은 무엇입니까?>, <Question: 2. 좋아하는 동물은 무엇입니까?>, <Question: 4. 여행가고 싶은 나라는?>, <Question: 3. 싫어하는 색깔은 고르세요.>]>"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["## QuerySet을 이용해서 정렬 (order by) \n", "##  - QS.order_by(\"기준Field명\"): ASC, QS.order_by(\"-기준Field명\"): DESC\n", "# result.order_by(\"question_text\")\n", "result.order_by(\"-question_text\")"]}, {"cell_type": "code", "execution_count": 38, "id": "4bdfb9d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10 개 0\n", "6 검정 0\n", "11 고양이 5\n", "9 곰 0\n", "3 노랑 0\n", "4 보라 0\n", "7 보라 0\n", "1 빨강 10\n", "5 빨강 0\n", "8 주황 0\n", "2 파랑 0\n", "12 호랑이 3\n"]}], "source": ["# Choice에 모든 데이터를 조회 -> choice_text 기준으로 정렬\n", "results = Choice.objects.all().order_by(\"choice_text\")\n", "for c in results:\n", "    print(c.pk, c.choice_text, c.votes)"]}, {"cell_type": "code", "execution_count": 41, "id": "dd07d0e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10 개 0\n", "6 검정 0\n", "11 고양이 5\n", "9 곰 0\n", "3 노랑 0\n", "7 보라 20\n", "4 보라 0\n", "5 빨강 10\n", "1 빨강 5\n", "8 주황 0\n", "2 파랑 0\n", "12 호랑이 3\n"]}], "source": ["# order by choice_text, votes desc\n", "results = Choice.objects.all().order_by(\"choice_text\", \"-votes\")\n", "for c in results:\n", "    print(c.pk, c.choice_text, c.votes)"]}, {"cell_type": "code", "execution_count": 42, "id": "4dc1e048", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\", \"polls_choice\".\"choice_text\", \"polls_choice\".\"votes\", \"polls_choice\".\"question_id\" FROM \"polls_choice\" ORDER BY \"polls_choice\".\"choice_text\" ASC, \"polls_choice\".\"votes\" DESC\n"]}], "source": ["print(results.query)"]}, {"cell_type": "markdown", "id": "25950d84", "metadata": {}, "source": ["### Where 절\n", "- filter()\n", "  - 조회조건이 True인 행들을 조회 -> QuerySet을 반환\n", "- exclude()\n", "  - 조회조건이 False인 행들을 조회 -> QuerySet을 반환\n", "- get()\n", "  - 조회조건이 True인 행이 1개일 때 조회.-> Model에 결과를 담아서 반환.\n", "  - 조회결과가 2행 이상이거나 없을 경우 Exception 발생.\n", "- **조회조건**\n", "  - `Field이름__비교연산자 = 비교할 값`"]}, {"cell_type": "code", "execution_count": null, "id": "14f25436", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'django.db.models.query.QuerySet'>\n", "<QuerySet [<Question: 2. 좋아하는 동물은 무엇입니까?>, <Question: 3. 싫어하는 색깔은 고르세요.>, <Question: 4. 여행가고 싶은 나라는?>]>\n"]}], "source": ["# pk 조회 -> 결과 1행 | 0행\n", "# 동등 비교: field명 = 값\n", "result = Question.objects.get(pk=1)    # where id = 1. 조회결과가 1개\n", "result = Question.objects.filter(pk=1) # where id = 1. 조회결과가 0개 이상\n", "result = Question.objects.exclude(pk=1)# where not id = 1\n", "\n", "print(type(result))\n", "print(result)\n", "\n"]}, {"cell_type": "code", "execution_count": 54, "id": "927cdce3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5. 빨강\n"]}], "source": ["# 비교 연산 \n", "result = Choice.objects.filter(pk__lt=5) # where pk < 5\n", "result = Choice.objects.filter(pk__lte=5) # where pk <= 5\n", "result = Choice.objects.filter(pk__gt=5) # where pk > 5\n", "result = Choice.objects.filter(pk__gte=5) # where pk >= 5\n", "result = Choice.objects.filter(pk=5) # where pk = 5\n", "\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": null, "id": "495f285f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE \"polls_question\".\"question_text\" LIKE 싫어하는% ESCAPE '\\'\n", "3. 싫어하는 색깔은 고르세요.\n"]}], "source": ["# 문자열 부분일치 - like ( xxx를 포함, xxx로 시작, xxx로 끝)\n", "result = Question.objects.filter(question_text__contains='색깔')\n", "# question_text like '%색깔%'\n", "result = Question.objects.filter(question_text__endswith='무엇입니까?')\n", "# question_text like \"%무엇입니까?\"\n", "result = Question.objects.filter(question_text__startswith=\"싫어하는\")\n", "# question_text like \"싫어하는%\"\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 64, "id": "159b7288", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\", \"polls_choice\".\"choice_text\", \"polls_choice\".\"votes\", \"polls_choice\".\"question_id\" FROM \"polls_choice\" WHERE NOT (\"polls_choice\".\"id\" IN (1, 10, 3))\n", "2. 파랑\n", "4. 보라\n", "5. 빨강\n", "6. 검정\n", "7. 보라\n", "8. 주황\n", "9. 곰\n", "11. 고양이\n", "12. 호랑이\n"]}], "source": ["# in  연산\n", "result = Choice.objects.filter(pk__in=[1, 10, 3]) # pk in (1, 10, 3)\n", "result = Choice.objects.exclude(pk__in=[1, 10, 3]) # pk not in (1, 10, 3)\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 66, "id": "46b05052", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\", \"polls_choice\".\"choice_text\", \"polls_choice\".\"votes\", \"polls_choice\".\"question_id\" FROM \"polls_choice\" WHERE NOT (\"polls_choice\".\"id\" BETWEEN 4 AND 8)\n", "1. 빨강\n", "2. 파랑\n", "3. 노랑\n", "9. 곰\n", "10. 개\n", "11. 고양이\n", "12. 호랑이\n"]}], "source": ["# between\n", "result = Choice.objects.filter(pk__range=[4, 8])\n", "# pk between 4 and 8\n", "result = Choice.objects.exclude(pk__range=[4, 8])\n", "# pk not between 4 and 8\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "markdown", "id": "d02261ca", "metadata": {}, "source": ["#### where 절의 and, or\n", "- `and`: 조건들을 나열\n", "- 각 조건을 `Q()` 함수에 넣고 `|` 로 연결."]}, {"cell_type": "code", "execution_count": null, "id": "8fcf63b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (\"polls_question\".\"id\" >= 2 AND \"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\')\n", "2. 좋아하는 동물은 무엇입니까?\n"]}], "source": ["result = Question.objects.filter(\n", "    question_text__endswith='무엇입니까?',\n", "    pk__gte=2\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": null, "id": "edac95c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Q: (OR: ('question_text__endswith', '무엇입니까?'), ('pk__gte', 3))>"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["~Q(question_text__endswith='무엇입니까?')   # | Q(pk__gte=3)"]}, {"cell_type": "code", "execution_count": 3, "id": "7a2a1038", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (\"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\' OR \"polls_question\".\"id\" >= 3)\n", "1. 좋아하는 색깔은 무엇입니까?\n", "2. 좋아하는 동물은 무엇입니까?\n", "3. 싫어하는 색깔은 고르세요.\n", "4. 여행가고 싶은 나라는?\n"]}], "source": ["from django.db.models import Q\n", "result = Question.objects.filter(\n", "    Q(question_text__endswith='무엇입니까?') | Q(pk__gte=3)\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 5, "id": "1032b637", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (NOT (\"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\') OR \"polls_question\".\"id\" >= 4)\n", "3. 싫어하는 색깔은 고르세요.\n", "4. 여행가고 싶은 나라는?\n"]}], "source": ["# ~Q() : Not\n", "result = Question.objects.filter(\n", "    ~Q(question_text__endswith='무엇입니까?') | Q(pk__gte=4)\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 7, "id": "4e0b2987", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (NOT (\"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\') AND \"polls_question\".\"id\" <= 1)\n"]}], "source": ["result = Question.objects.filter(\n", "    ~Q(question_text__endswith='무엇입니까?'), pk__lte=3\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "markdown", "id": "2ca675e1", "metadata": {}, "source": ["#### 조회할 컬럼들을 선택.\n", "- `values(Field명, ...)`\n", "- 개별 조회 결과을 Dictionary로 반환."]}, {"cell_type": "code", "execution_count": 85, "id": "493f20c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\" AS \"pk\", \"polls_choice\".\"votes\" AS \"votes\" FROM \"polls_choice\" WHERE \"polls_choice\".\"id\" < 5\n"]}, {"data": {"text/plain": ["<QuerySet [{'pk': 1, 'votes': 5}, {'pk': 2, 'votes': 0}, {'pk': 3, 'votes': 0}, {'pk': 4, 'votes': 0}]>"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["result = Question.objects.all().values(\"pk\", \"question_text\")\n", "result = Choice.objects.filter(pk__lt = 5).values('pk', 'votes')\n", "# select pk, votes from choice where pk < 5\n", "print(result.query)\n", "result\n", "# for r in result:\n", "#     print(type(r), r['pk'], r['question_text'])"]}, {"cell_type": "markdown", "id": "9c7ad3b5", "metadata": {}, "source": ["### 집계함수\n", "- `aggregate(집계함수(집계기준field명), 집계함수(field명), ...)`\n", "  - `select avg(salary), count(comm_pct), max(salary) from emp`\n", "- **groupby**\n", "  - values(\"groupby기준 컬럼\").annotate(집계함수)"]}, {"cell_type": "code", "execution_count": 86, "id": "e263ac0a", "metadata": {}, "outputs": [], "source": ["from django.db.models import (\n", "    Count, # 값의 개수(null 제외한)\n", "    <PERSON><PERSON>,   # 합계\n", "    Avg,   # 평균\n", "    Min,   # 최소\n", "    <PERSON>,   # 최대값\n", "    <PERSON><PERSON><PERSON><PERSON>, # 표준편차\n", "    Variance # 분산값\n", ")"]}, {"cell_type": "code", "execution_count": 92, "id": "96a0960c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'votes__count': 12, 'votes__avg': 3.5833333333333335, 'pk__max': 12, 'pk__min': 1, 'votes__sum': 43, 'votes__stddev': 5.808877305947816, 'votes__variance': 33.74305555555556}\n"]}], "source": ["result = Choice.objects.aggregate(\n", "    Count(\"votes\"),\n", "    Avg(\"votes\"),\n", "    <PERSON>(\"pk\"),\n", "    Min(\"pk\"),\n", "    <PERSON><PERSON>(\"votes\"),\n", "    <PERSON><PERSON><PERSON><PERSON>(\"votes\"),\n", "    Variance(\"votes\")\n", "\n", ")\n", "\n", "print(result)\n", "# 반환: dictionary\n", "# default key: field명__집계명"]}, {"cell_type": "code", "execution_count": 93, "id": "6fa41d1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'cnt': 12, 'min': 0, 'max': 20}"]}, "execution_count": 93, "metadata": {}, "output_type": "execute_result"}], "source": ["result = Choice.objects.aggregate(\n", "    cnt=Count(\"votes\"),\n", "    min=Min(\"votes\"),\n", "    max=Max(\"votes\")\n", ")\n", "result"]}, {"cell_type": "code", "execution_count": 99, "id": "b1056721", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'min_max_diff': 11}"]}, "execution_count": 99, "metadata": {}, "output_type": "execute_result"}], "source": ["# 집계 결과를 연산\n", "# 변수명 = (집계함수 - 집계함수)\n", "Choice.objects.aggregate(min_max_diff = Max(\"pk\") - Min(\"pk\"))"]}, {"cell_type": "code", "execution_count": 101, "id": "2a2a9953", "metadata": {}, "outputs": [], "source": ["######### groupby + 집계\n", "result = Choice.objects.values(\"question\").annotate(\n", "    min=Min(\"votes\"),\n", "    max=Max(\"votes\")\n", ")"]}, {"cell_type": "code", "execution_count": 102, "id": "81e9e180", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [{'question': 1, 'min': 0, 'max': 5}, {'question': 2, 'min': 0, 'max': 5}, {'question': 3, 'min': 0, 'max': 20}]>"]}, "execution_count": 102, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "markdown", "id": "7c0509c4", "metadata": {}, "source": ["### JOIN\n", "- 자식 테이블(모델) 기준으로 부모 테이블(모델) 데이터 조회.\n", "- `자식모델객체.FK_Field`"]}, {"cell_type": "code", "execution_count": 14, "id": "85eec98f", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 1, '빨강', 5, <Question: 1. 좋아하는 색깔은 무엇입니까?>)"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# Choice(자식) --> Question(부모)\n", "c1 = Choice.objects.get(pk=1)\n", "c1.pk, c1.id, c1.choice_text, c1.votes, c1.question"]}, {"cell_type": "code", "execution_count": 17, "id": "5477beb6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1,\n", " '좋아하는 색깔은 무엇입니까?',\n", " datetime.datetime(2025, 7, 7, 5, 17, 2, 657964, tzinfo=datetime.timezone.utc))"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# c1이 참조하는 question의 정보\n", "c1.question.pk, c1.question.question_text, c1.question.pub_date"]}, {"cell_type": "code", "execution_count": 22, "id": "f9da62d7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["질문:좋아하는 색깔은 무엇입니까?, 조회한 보기:빨강\n", "질문:좋아하는 색깔은 무엇입니까?, 조회한 보기:파랑\n", "질문:좋아하는 색깔은 무엇입니까?, 조회한 보기:노랑\n", "질문:좋아하는 색깔은 무엇입니까?, 조회한 보기:보라\n", "질문:싫어하는 색깔은 고르세요., 조회한 보기:빨강\n"]}], "source": ["result_list = Choice.objects.filter(pk__lte=5)\n", "# 조회한 choice들의 질문 - 보기\n", "for result in result_list:\n", "    print(f\"질문:{result.question.question_text}, 조회한 보기:{result.choice_text}\")"]}, {"cell_type": "markdown", "id": "613dece6", "metadata": {}, "source": ["- 부모테이블(모델) 기준으로 자식테이블의 데이터(모델)를 조회\n", "  - `부모모델객체.자식모델클래스이름(소문자)_set`을 통해서 부모객체를 참조하는 자식 데이터들을 **조회**할 수 있다."]}, {"cell_type": "code", "execution_count": 24, "id": "12aa2c7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 좋아하는 색깔은 무엇입니까? 2025-07-07 05:17:02.657964+00:00\n"]}], "source": ["q1 = Question.objects.get(pk=1)\n", "q1\n", "print(q1.pk, q1.question_text, q1.pub_date)"]}, {"cell_type": "code", "execution_count": 27, "id": "afbbfc9e", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [<Choice: 1. 빨강>, <Choice: 2. 파랑>, <Choice: 3. 노랑>, <Choice: 4. 보라>]>"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["# RelatedManager \n", "#-> 부모객체(q1)와 관련있는 자식의 데이터들 안에서만 조회할 수있는 Model Manager\n", "q1.choice_set\n", "\n", "choice_list = q1.choice_set.all()\n", "choice_list\n"]}, {"cell_type": "code", "execution_count": 29, "id": "8421f393", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["질문: 좋아하는 색깔은 무엇입니까?\n", "보기\n", "1. 빨강, 5\n", "2. 파랑, 0\n", "3. 노랑, 0\n", "4. 보라, 0\n"]}], "source": ["print(\"질문:\", q1.question_text)\n", "print(\"보기\")\n", "for c in choice_list:\n", "    print(f\"{c.pk}. {c.choice_text}, {c.votes}\")"]}, {"cell_type": "code", "execution_count": 33, "id": "fe3c8252", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 좋아하는 색깔은 무엇입니까?\n", "\t1. 빨강 - 5\n", "\t2. 파랑 - 0\n", "\t3. 노랑 - 0\n", "\t4. 보라 - 0\n", "2. 좋아하는 동물은 무엇입니까?\n", "\t1. 곰 - 0\n", "\t2. 개 - 0\n", "\t3. 고양이 - 5\n", "\t4. 호랑이 - 3\n", "3. 싫어하는 색깔은 고르세요.\n", "\t1. 빨강 - 10\n", "\t2. 검정 - 0\n", "\t3. 보라 - 20\n", "\t4. 주황 - 0\n", "4. 여행가고 싶은 나라는?\n"]}], "source": ["#  전체 질문을 조회하고 그것에 대해서 위 형식으로 출력.\n", "q_list = Question.objects.all()\n", "for q in q_list:\n", "    print(f\"{q.pk}. {q.question_text}\")\n", "    c_list = q.choice_set.all()\n", "    for idx, c in enumerate(c_list, start=1):\n", "        print(f\"\\t{idx}. {c.choice_text} - {c.votes}\")"]}, {"cell_type": "markdown", "id": "f7330385", "metadata": {}, "source": ["# insert / update\n", "- `모델객체.save()`\n", "- 모델객체의 pk 가 DB에 없으면 insert, 있으면 update"]}, {"cell_type": "code", "execution_count": 36, "id": "ba22598f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["배우고 싶은 언어는 무엇입니까?\n", "None None\n"]}], "source": ["# insert\n", "## question: pk-id : 자동증가 정수(생략), pub_date: insert할 때 일시를 추가(생략)\n", "new_q = Question(question_text=\"배우고 싶은 언어는 무엇입니까?\")\n", "print(new_q.question_text)\n", "print(new_q.pk, new_q.pub_date)"]}, {"cell_type": "code", "execution_count": 37, "id": "5dc86ba8", "metadata": {}, "outputs": [], "source": ["# 저장 - pk: None (DB에 없는 데이터) ==> insert\n", "new_q.save()"]}, {"cell_type": "code", "execution_count": 38, "id": "f591a6a3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n", "2025-07-08 02:22:32.175623+00:00\n"]}], "source": ["# insert 후에 자동저장되는 값들(pk, pub_date)이 모델객체의 field에 저장된다.\n", "print(new_q.pk)\n", "print(new_q.pub_date)"]}, {"cell_type": "code", "execution_count": 44, "id": "fed50958", "metadata": {}, "outputs": [], "source": ["# update\n", "q = Question.objects.get(pk=4)\n", "q.question_text = \"여행으로 가고 싶은 나라를 선택해주세요.\""]}, {"cell_type": "code", "execution_count": 46, "id": "5c6c5668", "metadata": {}, "outputs": [], "source": ["# q.pk = 4 -> DB에 있는 pk => update\n", "q.save() "]}, {"cell_type": "code", "execution_count": 47, "id": "b562c686", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 좋아하는 색깔은 무엇입니까?\n", "2. 좋아하는 동물은 무엇입니까?\n", "3. 싫어하는 색깔은 고르세요.\n", "4. 여행으로 가고 싶은 나라를 선택해주세요.\n", "5. 배우고 싶은 언어는 무엇입니까?\n"]}], "source": ["for q in Question.objects.all():\n", "    print(q)"]}, {"cell_type": "code", "execution_count": null, "id": "d0fbd927", "metadata": {}, "outputs": [], "source": ["# pk=4인 질문의 보기(choice)를 추가\n", "q = Question.objects.get(pk=4)"]}, {"cell_type": "code", "execution_count": 59, "id": "fae96a8b", "metadata": {}, "outputs": [], "source": ["# FK field -> 부모 Model 객체 (참조 Field값만 instance변수로 가지면 된다.)\n", "c = Choice(choice_text=\"미국\", question=q)\n", "c.save()"]}, {"cell_type": "code", "execution_count": 61, "id": "bdd952e1", "metadata": {}, "outputs": [], "source": ["c = Choice(choice_text=\"일본\", votes=10, question=q)\n", "c.save()"]}, {"cell_type": "code", "execution_count": null, "id": "3c2ed82a", "metadata": {}, "outputs": [], "source": ["qq = Question(pk=4)  # 부모 모델 객체는 PK만 있으면 된다.\n", "# print(qq.pk, qq.question_text)\n", "c = Choice(choice_text=\"영국\", votes=20, question=qq)\n", "c.save()"]}, {"cell_type": "code", "execution_count": 67, "id": "d340b5de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["13 미국 0\n", "14 일본 10\n", "15 영국 20\n"]}], "source": ["for c in q.choice_set.all():\n", "    print(c.pk, c.choice_text, c.votes)"]}, {"cell_type": "markdown", "id": "6f1c3ec4", "metadata": {}, "source": ["# delete\n", "- `모델객체.delete()`\n", "  - 모델객체의 pk의 data를 DB에 삭제.\n"]}, {"cell_type": "code", "execution_count": 69, "id": "62ef273c", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, {'polls.Choice': 1})"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["# delete from choice where id=15\n", "c15 = Choice.objects.get(pk=15)\n", "c15.delete()"]}, {"cell_type": "code", "execution_count": 72, "id": "b133e2d7", "metadata": {}, "outputs": [], "source": ["# choice에서 id가 10 이상인 값들을 삭제\n", "## delete from choice where id >= 10\n", "del_c = Choice.objects.filter(pk__gte=10)\n", "for c in del_c:\n", "    c.delete()"]}, {"cell_type": "code", "execution_count": 73, "id": "4ed0515e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 빨강\n", "2. 파랑\n", "3. 노랑\n", "4. 보라\n", "5. 빨강\n", "6. 검정\n", "7. 보라\n", "8. 주황\n", "9. 곰\n"]}], "source": ["for c in Choice.objects.all():\n", "    print(c)"]}, {"cell_type": "markdown", "id": "5fad604d", "metadata": {}, "source": ["# 직접 SQL문을 실행"]}, {"cell_type": "code", "execution_count": 79, "id": "3e248cd2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<RawQuerySet: select * from polls_question>\n", "1. 좋아하는 색깔은 무엇입니까?\n", "2. 좋아하는 동물은 무엇입니까?\n", "3. 싫어하는 색깔은 고르세요.\n", "4. 여행으로 가고 싶은 나라를 선택해주세요.\n", "5. 배우고 싶은 언어는 무엇입니까?\n"]}], "source": ["result = Question.objects.raw(\"select * from polls_question\")\n", "\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 77, "id": "2b074827", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 빨강 1. 좋아하는 색깔은 무엇입니까?\n", "2. 파랑 1. 좋아하는 색깔은 무엇입니까?\n", "3. 노랑 1. 좋아하는 색깔은 무엇입니까?\n", "4. 보라 1. 좋아하는 색깔은 무엇입니까?\n", "5. 빨강 3. 싫어하는 색깔은 고르세요.\n", "6. 검정 3. 싫어하는 색깔은 고르세요.\n", "7. 보라 3. 싫어하는 색깔은 고르세요.\n", "8. 주황 3. 싫어하는 색깔은 고르세요.\n", "9. 곰 2. 좋아하는 동물은 무엇입니까?\n"]}], "source": ["result = Choice.objects.raw(\"select * from polls_choice\")\n", "for r in result:\n", "    print(r, r.question)"]}, {"cell_type": "code", "execution_count": null, "id": "443268b8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "1f5e2fb0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 82, "id": "0d951521", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 좋아하는 색깔은 무엇입니까?\n", "2 좋아하는 동물은 무엇입니까?\n", "3 싫어하는 색깔은 고르세요.\n", "4 여행으로 가고 싶은 나라를 선택해주세요.\n", "5 배우고 싶은 언어는 무엇입니까?\n"]}], "source": ["for q in Question.objects.all():\n", "    print(q.pk, q.question_text)"]}, {"cell_type": "code", "execution_count": 92, "id": "48e07575", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 좋아하는 색깔은 무엇입니까? 2025-07-07 05:17:02.657964+00:00\n", "1 빨강\n", "2 파랑\n", "3 노랑\n", "4 보라\n"]}], "source": ["q = Question.objects.get(pk=1)\n", "print(q.pk, q.question_text, q.pub_date)\n", "for c in q.choice_set.all():\n", "    print(c.pk, c.choice_text)"]}, {"cell_type": "code", "execution_count": 1, "id": "00bcc2f9", "metadata": {}, "outputs": [], "source": ["import socket\n", "\n", "def scan_with_hostnames(port=8000):\n", "    base_ip = \"192.168.0.\"\n", "    open_hosts = []\n", "\n", "    for i in range(2, 255):\n", "        ip = base_ip + str(i)\n", "        try:\n", "            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "            sock.settimeout(0.2)\n", "            result = sock.connect_ex((ip, port))\n", "            sock.close()\n", "\n", "            if result == 0:\n", "                try:\n", "                    hostname, _, _ = socket.gethostbyaddr(ip)\n", "                except socket.herror:\n", "                    hostname = \"Unknown\"\n", "                print(f\"✅ {ip}:{port} - Hostname: {hostname}\")\n", "                open_hosts.append((ip, hostname))\n", "\n", "        except:\n", "            continue\n", "\n", "    return open_hosts\n"]}, {"cell_type": "code", "execution_count": null, "id": "f72f3dcd", "metadata": {}, "outputs": [], "source": ["scan_with_hostnames()"]}], "metadata": {"kernelspec": {"display_name": "lang_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}