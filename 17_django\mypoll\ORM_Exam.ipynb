# mypoll/ORM_Exam.ipynb

## Jupyter Lab에서 Django shell을 실행하기 위한 설정
import os
import django
# 환경변수로 config/settings.py의 위치를 설정
os.environ['DJANGO_SETTINGS_MODULE'] = "config.settings"
os.environ['DJANGO_ALLOW_ASYNC_UNSAFE'] = "true"

django.setup()

# 조회 테스트
from polls.models import Question, Choice

q = Question.objects.all()
q



from polls.models import Question, Choice

model_manager = Question.objects
type(model_manager)

result = model_manager.all()
print("조회한 데이터개수:", len(result))
print("all()로 실행된 SQL문을 조회")
print(result.query)

print(type(result))
result

# QuerySet -> Iterable
for q in result:
    # print(type(q))
    print(q, q.pub_date)

# QuerySet -> Subscriptable
q = result[1]
print(q.id, q.pk)
print(q.question_text, q.pub_date)
print(type(q.pk), type(q.question_text), type(q.pub_date))

# QuerySet - 첫번째, 마지막 index값 조회
q_s = result.first()
q_e = result.last()
q_s.pk, q_e.pk

# slicing -> 결과: list
s_result = result[:3]
print(type(s_result))
s_result

# 음수 indexing은 지원하지 않는다.
# result[-2] 

## QuerySet을 이용해서 정렬 (order by) 
##  - QS.order_by("기준Field명"): ASC, QS.order_by("-기준Field명"): DESC
# result.order_by("question_text")
result.order_by("-question_text")

# Choice에 모든 데이터를 조회 -> choice_text 기준으로 정렬
results = Choice.objects.all().order_by("choice_text")
for c in results:
    print(c.pk, c.choice_text, c.votes)

# order by choice_text, votes desc
results = Choice.objects.all().order_by("choice_text", "-votes")
for c in results:
    print(c.pk, c.choice_text, c.votes)

print(results.query)

# pk 조회 -> 결과 1행 | 0행
# 동등 비교: field명 = 값
result = Question.objects.get(pk=1)    # where id = 1. 조회결과가 1개
result = Question.objects.filter(pk=1) # where id = 1. 조회결과가 0개 이상
result = Question.objects.exclude(pk=1)# where not id = 1

print(type(result))
print(result)



# 비교 연산 
result = Choice.objects.filter(pk__lt=5) # where pk < 5
result = Choice.objects.filter(pk__lte=5) # where pk <= 5
result = Choice.objects.filter(pk__gt=5) # where pk > 5
result = Choice.objects.filter(pk__gte=5) # where pk >= 5
result = Choice.objects.filter(pk=5) # where pk = 5

for r in result:
    print(r)

# 문자열 부분일치 - like ( xxx를 포함, xxx로 시작, xxx로 끝)
result = Question.objects.filter(question_text__contains='색깔')
# question_text like '%색깔%'
result = Question.objects.filter(question_text__endswith='무엇입니까?')
# question_text like "%무엇입니까?"
result = Question.objects.filter(question_text__startswith="싫어하는")
# question_text like "싫어하는%"

print(result.query)
for r in result:
    print(r)

# in  연산
result = Choice.objects.filter(pk__in=[1, 10, 3]) # pk in (1, 10, 3)
result = Choice.objects.exclude(pk__in=[1, 10, 3]) # pk not in (1, 10, 3)

print(result.query)
for r in result:
    print(r)

# between
result = Choice.objects.filter(pk__range=[4, 8])
# pk between 4 and 8
result = Choice.objects.exclude(pk__range=[4, 8])
# pk not between 4 and 8

print(result.query)
for r in result:
    print(r)

result = Question.objects.filter(
    question_text__endswith='무엇입니까?',
    pk__gte=2
)

print(result.query)
for r in result:
    print(r)

~Q(question_text__endswith='무엇입니까?')   # | Q(pk__gte=3)

from django.db.models import Q
result = Question.objects.filter(
    Q(question_text__endswith='무엇입니까?') | Q(pk__gte=3)
)

print(result.query)
for r in result:
    print(r)

# ~Q() : Not
result = Question.objects.filter(
    ~Q(question_text__endswith='무엇입니까?') | Q(pk__gte=4)
)

print(result.query)
for r in result:
    print(r)

result = Question.objects.filter(
    ~Q(question_text__endswith='무엇입니까?'), pk__lte=3
)

print(result.query)
for r in result:
    print(r)

result = Question.objects.all().values("pk", "question_text")
result = Choice.objects.filter(pk__lt = 5).values('pk', 'votes')
# select pk, votes from choice where pk < 5
print(result.query)
result
# for r in result:
#     print(type(r), r['pk'], r['question_text'])

from django.db.models import (
    Count, # 값의 개수(null 제외한)
    Sum,   # 합계
    Avg,   # 평균
    Min,   # 최소
    Max,   # 최대값
    StdDev, # 표준편차
    Variance # 분산값
)

result = Choice.objects.aggregate(
    Count("votes"),
    Avg("votes"),
    Max("pk"),
    Min("pk"),
    Sum("votes"),
    StdDev("votes"),
    Variance("votes")

)

print(result)
# 반환: dictionary
# default key: field명__집계명

result = Choice.objects.aggregate(
    cnt=Count("votes"),
    min=Min("votes"),
    max=Max("votes")
)
result

# 집계 결과를 연산
# 변수명 = (집계함수 - 집계함수)
Choice.objects.aggregate(min_max_diff = Max("pk") - Min("pk"))

######### groupby + 집계
result = Choice.objects.values("question").annotate(
    min=Min("votes"),
    max=Max("votes")
)

result

# Choice(자식) --> Question(부모)
c1 = Choice.objects.get(pk=1)
c1.pk, c1.id, c1.choice_text, c1.votes, c1.question

# c1이 참조하는 question의 정보
c1.question.pk, c1.question.question_text, c1.question.pub_date

result_list = Choice.objects.filter(pk__lte=5)
# 조회한 choice들의 질문 - 보기
for result in result_list:
    print(f"질문:{result.question.question_text}, 조회한 보기:{result.choice_text}")

q1 = Question.objects.get(pk=1)
q1
print(q1.pk, q1.question_text, q1.pub_date)

# RelatedManager 
#-> 부모객체(q1)와 관련있는 자식의 데이터들 안에서만 조회할 수있는 Model Manager
q1.choice_set

choice_list = q1.choice_set.all()
choice_list


print("질문:", q1.question_text)
print("보기")
for c in choice_list:
    print(f"{c.pk}. {c.choice_text}, {c.votes}")

#  전체 질문을 조회하고 그것에 대해서 위 형식으로 출력.
q_list = Question.objects.all()
for q in q_list:
    print(f"{q.pk}. {q.question_text}")
    c_list = q.choice_set.all()
    for idx, c in enumerate(c_list, start=1):
        print(f"\t{idx}. {c.choice_text} - {c.votes}")

# insert
## question: pk-id : 자동증가 정수(생략), pub_date: insert할 때 일시를 추가(생략)
new_q = Question(question_text="배우고 싶은 언어는 무엇입니까?")
print(new_q.question_text)
print(new_q.pk, new_q.pub_date)

# 저장 - pk: None (DB에 없는 데이터) ==> insert
new_q.save()

# insert 후에 자동저장되는 값들(pk, pub_date)이 모델객체의 field에 저장된다.
print(new_q.pk)
print(new_q.pub_date)

# update
q = Question.objects.get(pk=4)
q.question_text = "여행으로 가고 싶은 나라를 선택해주세요."

# q.pk = 4 -> DB에 있는 pk => update
q.save() 

for q in Question.objects.all():
    print(q)

# pk=4인 질문의 보기(choice)를 추가
q = Question.objects.get(pk=4)

# FK field -> 부모 Model 객체 (참조 Field값만 instance변수로 가지면 된다.)
c = Choice(choice_text="미국", question=q)
c.save()

c = Choice(choice_text="일본", votes=10, question=q)
c.save()

qq = Question(pk=4)  # 부모 모델 객체는 PK만 있으면 된다.
# print(qq.pk, qq.question_text)
c = Choice(choice_text="영국", votes=20, question=qq)
c.save()

for c in q.choice_set.all():
    print(c.pk, c.choice_text, c.votes)

# delete from choice where id=15
c15 = Choice.objects.get(pk=15)
c15.delete()

# choice에서 id가 10 이상인 값들을 삭제
## delete from choice where id >= 10
del_c = Choice.objects.filter(pk__gte=10)
for c in del_c:
    c.delete()

for c in Choice.objects.all():
    print(c)

result = Question.objects.raw("select * from polls_question")

for r in result:
    print(r)

result = Choice.objects.raw("select * from polls_choice")
for r in result:
    print(r, r.question)





for q in Question.objects.all():
    print(q.pk, q.question_text)

q = Question.objects.get(pk=1)
print(q.pk, q.question_text, q.pub_date)
for c in q.choice_set.all():
    print(c.pk, c.choice_text)

import socket
import time

def scan_lan_for_open_8000(verbose=True):
    base_ip = "192.168.0."
    port = 8000
    open_hosts = []

    start = 1
    end = 254
    group_size = 10  # 중간 출력 구간

    print("🔍 포트 8000에 대해 스캔 시작...")

    for group_start in range(start, end + 1, group_size):
        group_end = min(group_start + group_size - 1, end)
        print(f"🌀 {base_ip}{group_start} ~ {base_ip}{group_end} 탐색 중...")

        for i in range(group_start, group_end + 1):
            ip = base_ip + str(i)
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.settimeout(0.5)  # 응답 대기 시간
                    result = sock.connect_ex((ip, port))
                    if result == 0:
                        try:
                            hostname, _, _ = socket.gethostbyaddr(ip)
                        except socket.herror:
                            hostname = "Unknown"
                        print(f"✅ 발견: http://{ip}:{port}  (호스트: {hostname})")
                        open_hosts.append((ip, hostname))
                    else:
                        if verbose:
                            print(f" - {ip}:{port} 닫힘")
            except Exception as e:
                print(f"⚠️ 오류 발생 ({ip}): {e}")
                continue

        # 중간 sleep을 넣을 수도 있음: time.sleep(0.5)

    print("\n🎯 스캔 완료.")

    if not open_hosts:
        print("❌ 열려있는 8000 포트 서버를 찾지 못했습니다.")
    else:
        print(f"\n📋 발견된 서버 {len(open_hosts)}개:")
        for ip, hostname in open_hosts:
            print(f" - http://{ip}:{port} ({hostname})")

    return open_hosts

# 실행
scan_lan_for_open_8000()
