{"cells": [{"cell_type": "code", "execution_count": 5, "id": "bb3c23fd", "metadata": {}, "outputs": [], "source": ["# mypoll/ORM_Exam.ipynb\n", "\n", "## <PERSON><PERSON>ter Lab에서 Django shell을 실행하기 위한 설정\n", "import os\n", "import django\n", "# 환경변수로 config/settings.py의 위치를 설정\n", "os.environ['DJANGO_SETTINGS_MODULE'] = \"config.settings\"\n", "os.environ['DJAN<PERSON><PERSON>_ALLOW_ASYNC_UNSAFE'] = \"true\"\n", "\n", "django.setup()"]}, {"cell_type": "code", "execution_count": 6, "id": "141aa6dd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\python.exe\n", "Name: <PERSON><PERSON><PERSON>\n", "Version: 5.2.4\n", "Summary: A high-level Python web framework that encourages rapid development and clean, pragmatic design.\n", "Home-page: https://www.djangoproject.com/\n", "Author: \n", "Author-email: Django Software Foundation <<EMAIL>>\n", "License: BSD-3-<PERSON>e\n", "Location: c:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\Lib\\site-packages\n", "Requires: <PERSON><PERSON><PERSON><PERSON>, sq<PERSON><PERSON><PERSON>, t<PERSON><PERSON>\n", "Required-by: \n"]}], "source": ["import sys\n", "print(sys.executable)\n", "!{sys.executable} -m pip show django\n"]}, {"cell_type": "code", "execution_count": 4, "id": "446cb007", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting django\n", "  Using cached django-5.2.4-py3-none-any.whl.metadata (4.1 kB)\n", "Requirement already satisfied: asgiref>=3.8.1 in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\lang_env\\lib\\site-packages (from django) (3.8.1)\n", "Collecting sqlparse>=0.3.1 (from django)\n", "  Using cached sqlparse-0.5.3-py3-none-any.whl.metadata (3.9 kB)\n", "Requirement already satisfied: tzdata in c:\\users\\<USER>\\appdata\\local\\miniconda3\\envs\\lang_env\\lib\\site-packages (from django) (2025.2)\n", "Using cached django-5.2.4-py3-none-any.whl (8.3 MB)\n", "Using cached sqlparse-0.5.3-py3-none-any.whl (44 kB)\n", "Installing collected packages: sqlparse, django\n", "\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   ---------------------------------------- 0/2 [sqlparse]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   -------------------- ------------------- 1/2 [django]\n", "   ---------------------------------------- 2/2 [django]\n", "\n", "Successfully installed django-5.2.4 sqlparse-0.5.3\n"]}], "source": ["!pip install django"]}, {"cell_type": "code", "execution_count": 14, "id": "5eaa3be5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [<Question: 1. 질문1 이름은?>]>"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# 조회 테스트\n", "from polls.models import Question, Choice\n", "\n", "q = Question.objects.all()\n", "q"]}, {"cell_type": "markdown", "id": "56ed7a08", "metadata": {}, "source": ["# 조회\n", "- ModelClass.objects -> Model Manager를 반환.\n", "- ModelManager: SQL작업을 할 수있는 메소드들을 제공하는 객체\n", "\n", "## 조회메소드\n", "- `all()`: 전체 조회\n", "- `filter()`, `exclude()`: 조건으로 조회(where절)\n", "- `get()`: 조회결과가 하나인 조건으로 조회(PK로 조회)\n", "\n", "## 조회결과\n", "- `QuerySet` 객체: 조회결과가 여러개일때 QuerySet에 모아서 반환.\n", "  - 조회결과를 바탕으로 추가 DB 작업을 진행할 수 있는 메소드들을 제공.\n", "  - 개별 데이터는 Model 객체에 담아서 반환.\n", "- Model 객체: 조회결과가 하나(`get()`) 일 때 "]}, {"cell_type": "code", "execution_count": 15, "id": "b836bace", "metadata": {}, "outputs": [], "source": ["from polls.models import Question, Choice"]}, {"cell_type": "code", "execution_count": 16, "id": "102b4553", "metadata": {}, "outputs": [{"data": {"text/plain": ["django.db.models.manager.Manager"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["model_manager = Question.objects\n", "type(model_manager)"]}, {"cell_type": "code", "execution_count": 17, "id": "63b472ec", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["조회한 데이터개수: 1\n", "all()로 실행된 SQL문을 조회\n", "SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\"\n"]}], "source": ["result = model_manager.all()\n", "print(\"조회한 데이터개수:\", len(result))\n", "print(\"all()로 실행된 SQL문을 조회\")\n", "print(result.query)"]}, {"cell_type": "code", "execution_count": 18, "id": "9ec1e39b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'django.db.models.query.QuerySet'>\n"]}, {"data": {"text/plain": ["<QuerySet [<Question: 1. 질문1 이름은?>]>"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["print(type(result))\n", "result"]}, {"cell_type": "code", "execution_count": 19, "id": "5a420166", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1. 질문1 이름은? 2025-07-08 01:06:23.662065+00:00\n"]}], "source": ["# QuerySet -> Iterable\n", "for q in result:\n", "    # print(type(q))\n", "    print(q, q.pub_date)"]}, {"cell_type": "code", "execution_count": 20, "id": "1c40dd0b", "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "list index out of range", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mIndexError\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[20]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# QuerySet -> Subscriptable\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m q = \u001b[43mresult\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m]\u001b[49m\n\u001b[32m      3\u001b[39m \u001b[38;5;28mprint\u001b[39m(q.id, q.pk)\n\u001b[32m      4\u001b[39m \u001b[38;5;28mprint\u001b[39m(q.question_text, q.pub_date)\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Users\\<USER>\\AppData\\Local\\miniconda3\\envs\\lang_env\\Lib\\site-packages\\django\\db\\models\\query.py:418\u001b[39m, in \u001b[36mQuerySet.__getitem__\u001b[39m\u001b[34m(self, k)\u001b[39m\n\u001b[32m    415\u001b[39m     \u001b[38;5;28;01m<PERSON>se\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mNegative indexing is not supported.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    417\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result_cache \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m418\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_result_cache\u001b[49m\u001b[43m[\u001b[49m\u001b[43mk\u001b[49m\u001b[43m]\u001b[49m\n\u001b[32m    420\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(k, \u001b[38;5;28mslice\u001b[39m):\n\u001b[32m    421\u001b[39m     qs = \u001b[38;5;28mself\u001b[39m._chain()\n", "\u001b[31mIndexError\u001b[39m: list index out of range"]}], "source": ["# QuerySet -> Subscriptable\n", "q = result[1]\n", "print(q.id, q.pk)\n", "print(q.question_text, q.pub_date)\n", "print(type(q.pk), type(q.question_text), type(q.pub_date))"]}, {"cell_type": "code", "execution_count": 21, "id": "399fd8d6", "metadata": {}, "outputs": [{"data": {"text/plain": ["(1, 1)"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["# QuerySet - 첫번째, 마지막 index값 조회\n", "q_s = result.first()\n", "q_e = result.last()\n", "q_s.pk, q_e.pk"]}, {"cell_type": "code", "execution_count": 22, "id": "0ac0cc15", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'list'>\n"]}, {"data": {"text/plain": ["[<Question: 1. 질문1 이름은?>]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# slicing -> 결과: list\n", "s_result = result[:3]\n", "print(type(s_result))\n", "s_result"]}, {"cell_type": "code", "execution_count": 23, "id": "536c262e", "metadata": {}, "outputs": [], "source": ["# 음수 indexing은 지원하지 않는다.\n", "# result[-2] "]}, {"cell_type": "code", "execution_count": 24, "id": "a2ccac0a", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [<Question: 1. 질문1 이름은?>]>"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["## QuerySet을 이용해서 정렬 (order by) \n", "##  - QS.order_by(\"기준Field명\"): ASC, QS.order_by(\"-기준Field명\"): DESC\n", "# result.order_by(\"question_text\")\n", "result.order_by(\"-question_text\")"]}, {"cell_type": "code", "execution_count": 25, "id": "4bdfb9d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 이름은 장고 1\n"]}], "source": ["# Choice에 모든 데이터를 조회 -> choice_text 기준으로 정렬\n", "results = Choice.objects.all().order_by(\"choice_text\")\n", "for c in results:\n", "    print(c.pk, c.choice_text, c.votes)"]}, {"cell_type": "code", "execution_count": 26, "id": "dd07d0e9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 이름은 장고 1\n"]}], "source": ["# order by choice_text, votes desc\n", "results = Choice.objects.all().order_by(\"choice_text\", \"-votes\")\n", "for c in results:\n", "    print(c.pk, c.choice_text, c.votes)"]}, {"cell_type": "code", "execution_count": 27, "id": "4dc1e048", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\", \"polls_choice\".\"choice_text\", \"polls_choice\".\"votes\", \"polls_choice\".\"question_id\" FROM \"polls_choice\" ORDER BY \"polls_choice\".\"choice_text\" ASC, \"polls_choice\".\"votes\" DESC\n"]}], "source": ["print(results.query)"]}, {"cell_type": "markdown", "id": "25950d84", "metadata": {}, "source": ["### Where 절\n", "- filter()\n", "  - 조회조건이 True인 행들을 조회 -> QuerySet을 반환\n", "- exclude()\n", "  - 조회조건이 False인 행들을 조회 -> QuerySet을 반환\n", "- get()\n", "  - 조회조건이 True인 행이 1개일 때 조회.-> Model에 결과를 담아서 반환.\n", "  - 조회결과가 2행 이상이거나 없을 경우 Exception 발생.\n", "- **조회조건**\n", "  - `Field이름__비교연산자 = 비교할 값`"]}, {"cell_type": "code", "execution_count": 28, "id": "14f25436", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'django.db.models.query.QuerySet'>\n", "<QuerySet []>\n"]}], "source": ["# pk 조회 -> 결과 1행 | 0행\n", "# 동등 비교: field명 = 값\n", "result = Question.objects.get(pk=1)    # where id = 1. 조회결과가 1개\n", "result = Question.objects.filter(pk=1) # where id = 1. 조회결과가 0개 이상\n", "result = Question.objects.exclude(pk=1)# where not id = 1\n", "\n", "print(type(result))\n", "print(result)\n", "\n"]}, {"cell_type": "code", "execution_count": 29, "id": "927cdce3", "metadata": {}, "outputs": [], "source": ["# 비교 연산 \n", "result = Choice.objects.filter(pk__lt=5) # where pk < 5\n", "result = Choice.objects.filter(pk__lte=5) # where pk <= 5\n", "result = Choice.objects.filter(pk__gt=5) # where pk > 5\n", "result = Choice.objects.filter(pk__gte=5) # where pk >= 5\n", "result = Choice.objects.filter(pk=5) # where pk = 5\n", "\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 30, "id": "495f285f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE \"polls_question\".\"question_text\" LIKE 싫어하는% ESCAPE '\\'\n"]}], "source": ["# 문자열 부분일치 - like ( xxx를 포함, xxx로 시작, xxx로 끝)\n", "result = Question.objects.filter(question_text__contains='색깔')\n", "# question_text like '%색깔%'\n", "result = Question.objects.filter(question_text__endswith='무엇입니까?')\n", "# question_text like \"%무엇입니까?\"\n", "result = Question.objects.filter(question_text__startswith=\"싫어하는\")\n", "# question_text like \"싫어하는%\"\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 64, "id": "159b7288", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\", \"polls_choice\".\"choice_text\", \"polls_choice\".\"votes\", \"polls_choice\".\"question_id\" FROM \"polls_choice\" WHERE NOT (\"polls_choice\".\"id\" IN (1, 10, 3))\n", "2. 파랑\n", "4. 보라\n", "5. 빨강\n", "6. 검정\n", "7. 보라\n", "8. 주황\n", "9. 곰\n", "11. 고양이\n", "12. 호랑이\n"]}], "source": ["# in  연산\n", "result = Choice.objects.filter(pk__in=[1, 10, 3]) # pk in (1, 10, 3)\n", "result = Choice.objects.exclude(pk__in=[1, 10, 3]) # pk not in (1, 10, 3)\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 66, "id": "46b05052", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\", \"polls_choice\".\"choice_text\", \"polls_choice\".\"votes\", \"polls_choice\".\"question_id\" FROM \"polls_choice\" WHERE NOT (\"polls_choice\".\"id\" BETWEEN 4 AND 8)\n", "1. 빨강\n", "2. 파랑\n", "3. 노랑\n", "9. 곰\n", "10. 개\n", "11. 고양이\n", "12. 호랑이\n"]}], "source": ["# between\n", "result = Choice.objects.filter(pk__range=[4, 8])\n", "# pk between 4 and 8\n", "result = Choice.objects.exclude(pk__range=[4, 8])\n", "# pk not between 4 and 8\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "markdown", "id": "d02261ca", "metadata": {}, "source": ["#### where 절의 and, or\n", "- `and`: 조건들을 나열\n", "- 각 조건을 `Q()` 함수에 넣고 `|` 로 연결."]}, {"cell_type": "code", "execution_count": 68, "id": "8fcf63b9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (\"polls_question\".\"id\" >= 2 AND \"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\')\n", "2. 좋아하는 동물은 무엇입니까?\n"]}], "source": ["result = Question.objects.filter(\n", "    question_text__endswith='무엇입니까?',\n", "    pk__gte=2\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 72, "id": "edac95c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["<Q: (OR: ('question_text__endswith', '무엇입니까?'), ('pk__gte', 3))>"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["Q(question_text__endswith='무엇입니까?') | Q(pk__gte=3)"]}, {"cell_type": "code", "execution_count": 71, "id": "7a2a1038", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (\"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\' OR \"polls_question\".\"id\" >= 3)\n", "1. 좋아하는 색깔은 무엇입니까?\n", "2. 좋아하는 동물은 무엇입니까?\n", "3. 싫어하는 색깔은 고르세요.\n", "4. 여행가고 싶은 나라는?\n"]}], "source": ["from django.db.models import Q\n", "result = Question.objects.filter(\n", "    Q(question_text__endswith='무엇입니까?') | Q(pk__gte=3)\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "code", "execution_count": 74, "id": "1032b637", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_question\".\"id\", \"polls_question\".\"question_text\", \"polls_question\".\"pub_date\" FROM \"polls_question\" WHERE (NOT (\"polls_question\".\"question_text\" LIKE %무엇입니까? ESCAPE '\\') OR \"polls_question\".\"id\" >= 4)\n", "3. 싫어하는 색깔은 고르세요.\n", "4. 여행가고 싶은 나라는?\n"]}], "source": ["# ~Q() : Not\n", "result = Question.objects.filter(\n", "    ~Q(question_text__endswith='무엇입니까?') | Q(pk__gte=4)\n", ")\n", "\n", "print(result.query)\n", "for r in result:\n", "    print(r)"]}, {"cell_type": "markdown", "id": "2ca675e1", "metadata": {}, "source": ["#### 조회할 컬럼들을 선택.\n", "- `values(Field명, ...)`\n", "- 개별 조회 결과을 Dictionary로 반환."]}, {"cell_type": "code", "execution_count": 85, "id": "493f20c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SELECT \"polls_choice\".\"id\" AS \"pk\", \"polls_choice\".\"votes\" AS \"votes\" FROM \"polls_choice\" WHERE \"polls_choice\".\"id\" < 5\n"]}, {"data": {"text/plain": ["<QuerySet [{'pk': 1, 'votes': 5}, {'pk': 2, 'votes': 0}, {'pk': 3, 'votes': 0}, {'pk': 4, 'votes': 0}]>"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["result = Question.objects.all().values(\"pk\", \"question_text\")\n", "result = Choice.objects.filter(pk__lt = 5).values('pk', 'votes')\n", "# select pk, votes from choice where pk < 5\n", "print(result.query)\n", "result\n", "# for r in result:\n", "#     print(type(r), r['pk'], r['question_text'])"]}, {"cell_type": "markdown", "id": "9c7ad3b5", "metadata": {}, "source": ["### 집계함수\n", "- `aggregate(집계함수(집계기준field명), 집계함수(field명), ...)`\n", "  - `select avg(salary), count(comm_pct), max(salary) from emp`\n", "- **groupby**\n", "  - values(\"groupby기준 컬럼\").annotate(집계함수)"]}, {"cell_type": "code", "execution_count": 31, "id": "e263ac0a", "metadata": {}, "outputs": [], "source": ["from django.db.models import (\n", "    Count, # 값의 개수(null 제외한)\n", "    <PERSON><PERSON>,   # 합계\n", "    Avg,   # 평균\n", "    Min,   # 최소\n", "    <PERSON>,   # 최대값\n", "    <PERSON><PERSON><PERSON><PERSON>, # 표준편차\n", "    Variance # 분산값\n", ")"]}, {"cell_type": "code", "execution_count": 32, "id": "96a0960c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'votes__count': 1, 'votes__avg': 1.0, 'pk__max': 1, 'pk__min': 1, 'votes__sum': 1, 'votes__stddev': 0.0, 'votes__variance': 0.0}\n"]}], "source": ["result = Choice.objects.aggregate(\n", "    Count(\"votes\"),\n", "    Avg(\"votes\"),\n", "    <PERSON>(\"pk\"),\n", "    Min(\"pk\"),\n", "    <PERSON><PERSON>(\"votes\"),\n", "    <PERSON><PERSON><PERSON><PERSON>(\"votes\"),\n", "    Variance(\"votes\")\n", "\n", ")\n", "\n", "print(result)\n", "# 반환: dictionary\n", "# default key: field명__집계명"]}, {"cell_type": "code", "execution_count": 33, "id": "6fa41d1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'cnt': 1, 'min': 1, 'max': 1}"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["result = Choice.objects.aggregate(\n", "    cnt=Count(\"votes\"),\n", "    min=Min(\"votes\"),\n", "    max=Max(\"votes\")\n", ")\n", "result"]}, {"cell_type": "code", "execution_count": 34, "id": "b1056721", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'min_max_diff': 0}"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["# 집계 결과를 연산\n", "# 변수명 = (집계함수 - 집계함수)\n", "Choice.objects.aggregate(min_max_diff = Max(\"pk\") - Min(\"pk\"))"]}, {"cell_type": "code", "execution_count": 35, "id": "2a2a9953", "metadata": {}, "outputs": [], "source": ["######### groupby + 집계\n", "result = Choice.objects.values(\"question\").annotate(\n", "    min=Min(\"votes\"),\n", "    max=Max(\"votes\")\n", ")"]}, {"cell_type": "code", "execution_count": 36, "id": "81e9e180", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet [{'question': 1, 'min': 1, 'max': 1}]>"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["result"]}, {"cell_type": "code", "execution_count": 44, "id": "cbda81a3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4, 4, '경기', 5, <Question: 2. 사는 지역은?>)"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["c1 = Choice.objects.get(pk=4)\n", "c1.pk, c1.id, c1.choice_text, c1.votes, c1.question"]}, {"cell_type": "code", "execution_count": 49, "id": "24fc4422", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["질문1 이름은? 이름은 장고 1\n", "질문1 이름은? 2. 이름은 투코 2\n", "사는 지역은? 서울 3\n", "사는 지역은? 경기 5\n", "좋아하는 음료는? 보드카 2\n"]}], "source": ["result_list = Choice.objects.filter(pk__lte=5)\n", "\n", "for result in result_list:\n", "    print(result.question.question_text, result.choice_text, result.votes)\n"]}, {"cell_type": "code", "execution_count": 51, "id": "75aac417", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 질문1 이름은? 2025-07-08 01:06:23.662065+00:00\n"]}], "source": ["# 부모테이블 기준으로 자식테이블의 데이터(모델)를 조회\n", "# - 부모모델객체. 자식 모델 클래스의이름(소문자).set'을 통해서 부모객체를 \n", "#   참조하는 자식 데이터들을 **조회**할 수 있다.\n", "\n", "q1 = Question.objects.get(pk=1)\n", "print(q1.pk, q1.question_text, q1.pub_date)"]}, {"cell_type": "code", "execution_count": 54, "id": "0c4dab19", "metadata": {}, "outputs": [{"data": {"text/plain": ["<django.db.models.fields.related_descriptors.create_reverse_many_to_one_manager.<locals>.RelatedManager at 0x221ef3c5250>"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["q1.choice_set"]}, {"cell_type": "code", "execution_count": 56, "id": "d292afae", "metadata": {}, "outputs": [], "source": ["# insert\n", "new_q = Question(question_text=\"배우고 싶은 언어는 무엇입니까?\")\n", "new_q.save()"]}, {"cell_type": "code", "execution_count": null, "id": "dc0e1f5a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["4 배우고 싶은 언어는 무엇입니까? 2025-07-08 02:22:58.108693+00:00\n"]}], "source": ["# insert후에 자동저장되는 값들이 모델객체의 field에 저장된다.\n", "print(new_q.pk, new_q.question_text, new_q.pub_date)\n"]}, {"cell_type": "code", "execution_count": 58, "id": "752c6a7e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 질문1 이름은? 2025-07-08 01:06:23.662065+00:00\n", "2 사는 지역은? 2025-07-08 01:22:52.284642+00:00\n", "3 좋아하는 음료는? 2025-07-08 01:24:01.634678+00:00\n", "4 배우고 싶은 언어는 무엇입니까? 2025-07-08 02:22:58.108693+00:00\n"]}], "source": ["for q in Question.objects.all():\n", "    print(q.pk, q.question_text, q.pub_date)"]}, {"cell_type": "code", "execution_count": null, "id": "c5f9f4f8", "metadata": {}, "outputs": [{"data": {"text/plain": ["<QuerySet []>"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["q = Question.objects.get(pk=4)\n", "q.question_text = \"여행 가곳픈곳 정해\""]}, {"cell_type": "code", "execution_count": 60, "id": "88e16178", "metadata": {}, "outputs": [], "source": ["c = Choice(choice_text=\"미국\", votes=10, question=q)\n", "c.save()"]}, {"cell_type": "code", "execution_count": 61, "id": "44b6b1ed", "metadata": {}, "outputs": [], "source": ["c = Choice(choice_text=\"일본\", votes=6, question=q)\n", "c.save()"]}, {"cell_type": "code", "execution_count": 62, "id": "837d713a", "metadata": {}, "outputs": [], "source": ["c = Choice(choice_text=\"영국\", votes=3, question=q)\n", "c.save()"]}, {"cell_type": "code", "execution_count": 63, "id": "79e5a0d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["미국 10\n", "일본 6\n", "영국 3\n"]}], "source": ["for c in q.choice_set.all():\n", "    print(c.choice_text, c.votes)"]}, {"cell_type": "code", "execution_count": null, "id": "59e85610", "metadata": {}, "outputs": [], "source": ["#delete\n", "# - `모델 객체.detele()`"]}, {"cell_type": "code", "execution_count": 65, "id": "24195197", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'Question' object has no attribute 'question'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[65]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      2\u001b[39m result = Question.objects.raw(\u001b[33m\"\u001b[39m\u001b[33mselect * from polls_choice\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m r \u001b[38;5;129;01min\u001b[39;00m result :\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m     \u001b[38;5;28mprint\u001b[39m(r, \u001b[43mr\u001b[49m\u001b[43m.\u001b[49m\u001b[43mquestion\u001b[49m)\n", "\u001b[31mAttributeError\u001b[39m: 'Question' object has no attribute 'question'"]}], "source": ["# 직접 SQL구문 실행\n", "result = Question.objects.raw(\"select * from polls_choice\")\n", "for r in result :\n", "    print(r, r.question)"]}], "metadata": {"kernelspec": {"display_name": "lang_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}