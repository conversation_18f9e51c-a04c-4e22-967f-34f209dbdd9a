{% extends 'layouts/main_layout.html' %}

{% block title %}HOME{% endblock %}

{% block contents %}
    <h1>설문 앱입니다.</h1>
    목록에 다양한 설문 문항이 있습니다.
    <hr>
{% endblock contents %}


{% comment %}
    url-pattern (config.urls)
    path("",views.home, name="home")
    def home(request):
        return render(request, "home.html")
        단순히 template 실행결과를 응답하는 view==> tempalteView를 사용하면 View함수를 만들 필요 없다.
{% endcomment %}
