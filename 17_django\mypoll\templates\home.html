{% extends 'layouts/main_layout.html' %}

{% block title %}HOME{% endblock %}

{% block contents %}
    <div class="text-center">
        <div class="mb-4">
            <i class="bi bi-clipboard-data display-1 text-primary"></i>
        </div>
        <h1 class="display-4 fw-bold text-primary mb-4">설문조사 시스템에 오신 것을 환영합니다!</h1>
        <p class="lead text-muted mb-5">다양한 설문 문항을 통해 의견을 수집하고 분석할 수 있습니다.</p>

        <div class="row g-4 mt-4">
            <div class="col-md-6">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-body text-center p-4">
                        <i class="bi bi-list-ul display-4 text-info mb-3"></i>
                        <h5 class="card-title">설문조사 목록</h5>
                        <p class="card-text text-muted">등록된 설문조사를 확인하고 투표에 참여하세요.</p>
                        <a href="{% url 'polls:list' %}" class="btn btn-info btn-custom">
                            <i class="bi bi-arrow-right me-2"></i>목록 보기
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-body text-center p-4">
                        <i class="bi bi-plus-circle display-4 text-success mb-3"></i>
                        <h5 class="card-title">새 설문조사 만들기</h5>
                        <p class="card-text text-muted">새로운 설문조사를 생성하고 의견을 수집하세요.</p>
                        <a href="{% url 'polls:vote_create' %}" class="btn btn-success btn-custom">
                            <i class="bi bi-plus me-2"></i>설문 만들기
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock contents %}


{% comment %}
    url-pattern (config.urls)
    path("",views.home, name="home")
    def home(request):
        return render(request, "home.html")
        단순히 template 실행결과를 응답하는 view==> tempalteView를 사용하면 View함수를 만들 필요 없다.
{% endcomment %}
