{% extends 'layouts/main_layout.html' %}

{% block title %}투표 결과{% endblock %}

{% block contents %}
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold text-success">
                    <i class="bi bi-bar-chart me-3"></i>투표 결과
                </h1>
                <p class="lead text-muted">투표해주셔서 감사합니다!</p>
            </div>

            <div class="card shadow-lg border-0">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-success me-2">{{ question.pk }}</span>
                        {{ question.question_text }}
                    </h4>
                </div>

                <div class="card-body p-4">
                    {% with total_votes=question.choice_set.all|length %}
                        {% for choice in question.choice_set.all %}
                            {% with percentage=choice.votes|floatformat:0 %}
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="mb-0 fw-medium">{{ choice.choice_text }}</h6>
                                        <span class="badge bg-primary">{{ choice.votes }}표</span>
                                    </div>
                                    <div class="progress" style="height: 25px;">
                                        <div class="progress-bar bg-gradient"
                                             role="progressbar"
                                             style="width: {% if choice.votes > 0 %}{{ choice.votes|add:0 }}%{% else %}0%{% endif %}"
                                             aria-valuenow="{{ choice.votes }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100">
                                            {% if choice.votes > 0 %}{{ choice.votes }}표{% endif %}
                                        </div>
                                    </div>
                                </div>
                            {% endwith %}
                        {% endfor %}
                    {% endwith %}

                    <div class="text-center mt-4 pt-3 border-top">
                        <p class="text-muted mb-0">
                            <i class="bi bi-people me-1"></i>
                            총 투표수: <strong>{% for choice in question.choice_set.all %}{{ choice.votes|add:0 }}{% if not forloop.last %} + {% endif %}{% endfor %} =
                            {% with total=0 %}
                                {% for choice in question.choice_set.all %}
                                    {% with total=total|add:choice.votes %}{% endwith %}
                                {% endfor %}
                                {{ question.choice_set.all.0.votes|add:question.choice_set.all.1.votes|default:0 }}
                            {% endwith %}표</strong>
                        </p>
                    </div>
                </div>
            </div>

            <div class="text-center mt-4">
                <div class="btn-group" role="group">
                    <a href="{% url 'polls:list' %}" class="btn btn-outline-primary btn-custom">
                        <i class="bi bi-list-ul me-2"></i>목록으로
                    </a>
                    <a href="{% url 'polls:vote_form' question.pk %}" class="btn btn-outline-secondary btn-custom">
                        <i class="bi bi-arrow-repeat me-2"></i>다시 투표
                    </a>
                    <a href="{% url 'polls:vote_create' %}" class="btn btn-success btn-custom">
                        <i class="bi bi-plus-circle me-2"></i>새 설문 만들기
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .progress-bar {
            transition: width 1s ease-in-out;
            font-weight: 500;
        }
        .card {
            animation: fadeInUp 0.6s ease-out;
        }
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock contents %}