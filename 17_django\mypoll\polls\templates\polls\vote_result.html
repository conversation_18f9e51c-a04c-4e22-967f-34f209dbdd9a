{% extends 'layouts/main_layout.html' %}

{% block title %}투표 결과 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">투표 결과</h1>
                <p class="lead fw-normal text-muted mb-0">투표해주셔서 감사합니다! 결과를 확인해보세요.</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <!-- Results card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-1">설문 #{{ question.pk }} 결과</h4>
                                    <small class="text-white-50">{{ question.question_text }}</small>
                                </div>
                                <div class="badge bg-success">완료</div>
                            </div>
                        </div>

                        <div class="card-body p-4">
                            <!-- Question title -->
                            <div class="mb-4">
                                <h3 class="fw-bolder mb-2">{{ question.question_text }}</h3>
                                <p class="text-muted">투표 결과를 확인해보세요.</p>
                            </div>

                            <!-- Results -->
                            <div class="mb-4">
                                <h5 class="fw-bold mb-3">투표 결과</h5>
                                {% for choice in question.choice_set.all %}
                                    <div class="mb-3" data-votes="{{ choice.votes }}">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="fw-medium">{{ choice.choice_text }}</span>
                                            <div>
                                                <span class="badge bg-primary me-2">{{ choice.votes }}표</span>
                                                <small class="text-muted percentage-text">(--%)</small>
                                            </div>
                                        </div>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-primary progress-bar-animated"
                                                 role="progressbar"
                                                 style="width: 0%"
                                                 aria-valuenow="{{ choice.votes }}"
                                                 aria-valuemin="0"
                                                 aria-valuemax="100">
                                                <span class="progress-text">{{ choice.votes }}표</span>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Summary -->
                            <div class="bg-light rounded p-3 text-center">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="fw-bold text-primary">총 투표수</div>
                                        <div class="h4 mb-0">
                                            <span id="total-votes">0</span>표
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="fw-bold text-primary">선택지 수</div>
                                        <div class="h4 mb-0">{{ question.choice_set.all|length }}개</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="text-center mt-4">
                        <div class="d-grid gap-2 d-md-block">
                            <a href="{% url 'polls:list' %}" class="btn btn-outline-primary">
                                <i class="bi bi-list-ul me-2"></i>목록으로
                            </a>
                            <a href="{% url 'polls:vote_form' question.pk %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-repeat me-2"></i>다시 투표
                            </a>
                            <a href="{% url 'polls:vote_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>새 설문 만들기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .progress-bar {
            transition: width 2s ease-in-out;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-text {
            color: white;
            font-weight: 500;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .percentage-text {
            min-width: 40px;
            display: inline-block;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 모든 투표 데이터 수집
            const choiceElements = document.querySelectorAll('[data-votes]');
            const votes = Array.from(choiceElements).map(el => parseInt(el.dataset.votes));
            const totalVotes = votes.reduce((sum, vote) => sum + vote, 0);

            // 총 투표수 표시
            document.getElementById('total-votes').textContent = totalVotes;

            // 각 선택지의 백분율과 프로그레스 바 업데이트
            choiceElements.forEach((element, index) => {
                const votes = parseInt(element.dataset.votes);
                const percentage = totalVotes > 0 ? Math.round((votes / totalVotes) * 100) : 0;

                // 백분율 텍스트 업데이트
                const percentageText = element.querySelector('.percentage-text');
                percentageText.textContent = `(${percentage}%)`;

                // 프로그레스 바 애니메이션
                const progressBar = element.querySelector('.progress-bar');
                setTimeout(() => {
                    progressBar.style.width = `${percentage}%`;
                }, 300 + (index * 200)); // 순차적 애니메이션
            });
        });
    </script>
{% endblock contents %}