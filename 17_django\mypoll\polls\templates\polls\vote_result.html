{% extends 'layouts/main_layout.html' %}

{% block title %}투표 결과 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">투표 결과</h1>
                <p class="lead fw-normal text-muted mb-0">투표해주셔서 감사합니다! 결과를 확인해보세요.</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <!-- Results card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-1">설문 #{{ question.pk }} 결과</h4>
                                    <small class="text-white-50">{{ question.question_text }}</small>
                                </div>
                                <div class="badge bg-success">완료</div>
                            </div>
                        </div>

                        <div class="card-body p-4">
                            <!-- Question title -->
                            <div class="mb-4">
                                <h3 class="fw-bolder mb-2">{{ question.question_text }}</h3>
                                <p class="text-muted">투표 결과를 확인해보세요.</p>
                            </div>

                            <!-- Results -->
                            <div class="mb-4">
                                <h5 class="fw-bold mb-3">투표 결과</h5>
                                {% with total_votes=0 %}
                                    {% for choice in question.choice_set.all %}
                                        {% with total_votes=total_votes|add:choice.votes %}{% endwith %}
                                    {% endfor %}

                                    {% for choice in question.choice_set.all %}
                                        <div class="mb-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <span class="fw-medium">{{ choice.choice_text }}</span>
                                                <div>
                                                    <span class="badge bg-primary me-2">{{ choice.votes }}표</span>
                                                    <small class="text-muted">
                                                        {% if total_votes > 0 %}
                                                            ({{ choice.votes|floatformat:0|add:0|mul:100|div:total_votes|floatformat:1 }}%)
                                                        {% else %}
                                                            (0%)
                                                        {% endif %}
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-primary"
                                                     role="progressbar"
                                                     style="width: {% if total_votes > 0 %}{{ choice.votes|mul:100|div:total_votes }}%{% else %}0%{% endif %}"
                                                     aria-valuenow="{{ choice.votes }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="{{ total_votes }}">
                                                </div>
                                            </div>
                                        </div>
                                    {% endfor %}
                                {% endwith %}
                            </div>

                            <!-- Summary -->
                            <div class="bg-light rounded p-3 text-center">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="fw-bold text-primary">총 투표수</div>
                                        <div class="h4 mb-0">
                                            {% with total_votes=0 %}
                                                {% for choice in question.choice_set.all %}
                                                    {% with total_votes=total_votes|add:choice.votes %}{% endwith %}
                                                {% endfor %}
                                                {{ total_votes }}표
                                            {% endwith %}
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="fw-bold text-primary">선택지 수</div>
                                        <div class="h4 mb-0">{{ question.choice_set.all|length }}개</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="text-center mt-4">
                        <div class="d-grid gap-2 d-md-block">
                            <a href="{% url 'polls:list' %}" class="btn btn-outline-primary">
                                <i class="bi bi-list-ul me-2"></i>목록으로
                            </a>
                            <a href="{% url 'polls:vote_form' question.pk %}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-repeat me-2"></i>다시 투표
                            </a>
                            <a href="{% url 'polls:vote_create' %}" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>새 설문 만들기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .progress-bar {
            transition: width 1.5s ease-in-out;
        }

        .card {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
{% endblock contents %}