{% extends 'layouts/main_layout.html' %}

{% block title %}설문 폼{% endblock %}

{% block contents %}
    <h1>설문</h1>
    <h2>질문: {{question.pk}}.{{ question.question_text }}</h2>
    설문 등록일시 {{question.pub_date}}
    {% if error_message %}
        <p style="color:red; font-size:0.8em;">{{error_message}}</p>
    {% endif %}
    <form action="{% url 'polls:vote' %}" method="post">
        {% csrf_token %}

        <input type="hidden" name="question_id" value="{{question.pk}}">
        {% for choice in question.choice_set.all %}
            <label for="{{choice.pk}}">{{choice.choice_text}}</label>
            <input type="radio" name="choice" value="{{choice.pk}}" id="{{choice.pk}}"><br>
        {% endfor %}
        <button type="submit">투표</button>
        <button type="reset">초기화</button>
    </form>
{% endblock contents %}