{% extends 'layouts/main_layout.html' %}

{% block title %}설문조사 투표 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">설문조사 투표</h1>
                <p class="lead fw-normal text-muted mb-0">아래 질문에 대한 여러분의 의견을 선택해주세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <!-- Survey card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-1">설문 #{{ question.pk }}</h4>
                                    <small class="text-white-50">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ question.pub_date|date:"Y년 m월 d일 H:i" }}
                                    </small>
                                </div>
                                <div class="badge bg-light text-primary">투표 진행중</div>
                            </div>
                        </div>

                        <div class="card-body p-4">
                            <!-- Question -->
                            <div class="mb-4">
                                <h3 class="fw-bolder mb-3">{{ question.question_text }}</h3>
                                <p class="text-muted">아래 선택지 중 하나를 선택하여 투표해주세요.</p>
                            </div>

                            <!-- Error message -->
                            {% if error_message %}
                                <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <div>{{ error_message }}</div>
                                </div>
                            {% endif %}

                            <!-- Vote form -->
                            <form action="{% url 'polls:vote' %}" method="post">
                                {% csrf_token %}
                                <input type="hidden" name="question_id" value="{{ question.pk }}">

                                <div class="mb-4">
                                    <h5 class="fw-bold mb-3">선택지</h5>
                                    {% for choice in question.choice_set.all %}
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="radio" name="choice"
                                                   value="{{ choice.pk }}" id="choice{{ choice.pk }}"
                                                   style="transform: scale(1.2);">
                                            <label class="form-check-label fw-medium" for="choice{{ choice.pk }}"
                                                   style="margin-left: 0.5rem; cursor: pointer;">
                                                {{ choice.choice_text }}
                                            </label>
                                        </div>
                                    {% endfor %}
                                </div>

                                <!-- Action buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="reset" class="btn btn-outline-secondary me-md-2">
                                        <i class="bi bi-arrow-clockwise me-2"></i>초기화
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>투표하기
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="text-center mt-4">
                        <a href="{% url 'polls:list' %}" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>목록으로 돌아가기
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .form-check-label:hover {
            color: #0d6efd;
        }

        .form-check:hover .form-check-input {
            border-color: #0d6efd;
        }
    </style>
{% endblock contents %}