{% extends 'layouts/main_layout.html' %}

{% block title %}설문 폼{% endblock %}

{% block contents %}
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold text-primary">
                    <i class="bi bi-hand-index me-3"></i>설문조사 투표
                </h1>
            </div>

            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <span class="badge bg-light text-primary me-2">{{ question.pk }}</span>
                        {{ question.question_text }}
                    </h4>
                    <small class="text-light">
                        <i class="bi bi-calendar3 me-1"></i>
                        등록일시: {{ question.pub_date|date:"Y년 m월 d일 H:i" }}
                    </small>
                </div>

                <div class="card-body p-4">
                    {% if error_message %}
                        <div class="alert alert-danger d-flex align-items-center" role="alert">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            {{ error_message }}
                        </div>
                    {% endif %}

                    <form action="{% url 'polls:vote' %}" method="post">
                        {% csrf_token %}
                        <input type="hidden" name="question_id" value="{{ question.pk }}">

                        <div class="mb-4">
                            <h5 class="mb-3 text-secondary">선택지를 골라주세요:</h5>
                            {% for choice in question.choice_set.all %}
                                <div class="form-check mb-3 p-3 border rounded choice-option">
                                    <input class="form-check-input" type="radio" name="choice"
                                           value="{{ choice.pk }}" id="choice{{ choice.pk }}">
                                    <label class="form-check-label w-100" for="choice{{ choice.pk }}">
                                        <span class="fw-medium">{{ choice.choice_text }}</span>
                                    </label>
                                </div>
                            {% endfor %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="reset" class="btn btn-outline-secondary btn-custom me-md-2">
                                <i class="bi bi-arrow-clockwise me-2"></i>초기화
                            </button>
                            <button type="submit" class="btn btn-primary btn-custom">
                                <i class="bi bi-check-circle me-2"></i>투표하기
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="text-center mt-3">
                <a href="{% url 'polls:list' %}" class="btn btn-link">
                    <i class="bi bi-arrow-left me-1"></i>목록으로 돌아가기
                </a>
            </div>
        </div>
    </div>

    <style>
        .choice-option {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .choice-option:hover {
            background-color: #f8f9fa;
            border-color: #007bff !important;
        }
        .choice-option:has(input:checked) {
            background-color: #e3f2fd;
            border-color: #007bff !important;
        }
    </style>
{% endblock contents %}