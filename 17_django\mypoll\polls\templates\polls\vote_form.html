{% extends 'layouts/main_layout.html' %}

{% block title %}설문조사 투표 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">설문조사 투표</h1>
                <p class="lead fw-normal text-muted mb-0">아래 질문에 대한 여러분의 의견을 선택해주세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <!-- Survey card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="mb-1">설문 #{{ question.pk }}</h4>
                                    <small class="text-white-50">
                                        <i class="bi bi-calendar3 me-1"></i>
                                        {{ question.pub_date|date:"Y년 m월 d일 H:i" }}
                                    </small>
                                </div>
                                <div class="badge bg-light text-primary">투표 진행중</div>
                            </div>
                        </div>

                        <div class="card-body p-4">
                            <!-- Question -->
                            <div class="mb-4">
                                <h3 class="fw-bolder mb-3">{{ question.question_text }}</h3>
                                {% if user_vote %}
                                    <div class="alert alert-info d-flex align-items-center" role="alert">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        <div>
                                            <strong>이미 투표하셨습니다!</strong><br>
                                            선택하신 답변: <strong>{{ user_vote.choice.choice_text }}</strong><br>
                                            투표 시간: {{ user_vote.voted_at|date:"Y년 m월 d일 H:i" }}
                                        </div>
                                    </div>
                                    <p class="text-muted">한 설문조사당 한 번만 투표할 수 있습니다. 결과를 확인해보세요!</p>
                                {% else %}
                                    <p class="text-muted">아래 선택지 중 하나를 선택하여 투표해주세요.</p>
                                {% endif %}
                            </div>

                            <!-- Error message -->
                            {% if error_message %}
                                <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
                                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                    <div>{{ error_message }}</div>
                                </div>
                            {% endif %}

                            {% if user_vote %}
                                <!-- Already voted - show choices but disabled -->
                                <div class="mb-4">
                                    <h5 class="fw-bold mb-3">선택지 (투표 완료)</h5>
                                    {% for choice in question.choice_set.all %}
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="radio" name="choice"
                                                   value="{{ choice.pk }}" id="choice{{ choice.pk }}"
                                                   style="transform: scale(1.2);"
                                                   disabled
                                                   {% if choice.pk == user_vote.choice.pk %}checked{% endif %}>
                                            <label class="form-check-label fw-medium {% if choice.pk == user_vote.choice.pk %}text-primary fw-bold{% else %}text-muted{% endif %}"
                                                   for="choice{{ choice.pk }}"
                                                   style="margin-left: 0.5rem;">
                                                {{ choice.choice_text }}
                                                {% if choice.pk == user_vote.choice.pk %}
                                                    <i class="bi bi-check-circle-fill text-success ms-2"></i>
                                                {% endif %}
                                            </label>
                                        </div>
                                    {% endfor %}
                                </div>

                                <!-- Action buttons for already voted -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'polls:list' %}" class="btn btn-outline-secondary me-md-2">
                                        <i class="bi bi-list-ul me-2"></i>목록으로
                                    </a>
                                    <a href="{% url 'polls:vote_result' question.pk %}" class="btn btn-primary">
                                        <i class="bi bi-bar-chart me-2"></i>결과 보기
                                    </a>
                                </div>
                            {% else %}
                                <!-- Vote form for new vote -->
                                <form action="{% url 'polls:vote' %}" method="post">
                                    {% csrf_token %}
                                    <input type="hidden" name="question_id" value="{{ question.pk }}">

                                    <div class="mb-4">
                                        <h5 class="fw-bold mb-3">선택지</h5>
                                        {% for choice in question.choice_set.all %}
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="radio" name="choice"
                                                       value="{{ choice.pk }}" id="choice{{ choice.pk }}"
                                                       style="transform: scale(1.2);">
                                                <label class="form-check-label fw-medium" for="choice{{ choice.pk }}"
                                                       style="margin-left: 0.5rem; cursor: pointer;">
                                                    {{ choice.choice_text }}
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>

                                    <!-- Action buttons -->
                                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                        <button type="reset" class="btn btn-outline-secondary me-md-2">
                                            <i class="bi bi-arrow-clockwise me-2"></i>초기화
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-check-circle me-2"></i>투표하기
                                        </button>
                                    </div>
                                </form>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Navigation -->
                    <div class="text-center mt-4">
                        <a href="{% url 'polls:list' %}" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left me-2"></i>목록으로 돌아가기
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <style>
        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        .form-check-label:hover {
            color: #0d6efd;
        }

        .form-check:hover .form-check-input {
            border-color: #0d6efd;
        }
    </style>
{% endblock contents %}