from django.contrib import admin
from .models import Question, Choice, Vote
# Register your models here.


@admin.register(Question)
class QuestionAdmin(admin.ModelAdmin):
    list_display = ('id', 'question_text', 'pub_date')
    list_filter = ('pub_date',)
    search_fields = ('question_text',)
    ordering = ('-pub_date',)


@admin.register(Choice)
class ChoiceAdmin(admin.ModelAdmin):
    list_display = ('id', 'choice_text', 'question', 'votes')
    list_filter = ('question',)
    search_fields = ('choice_text',)


@admin.register(Vote)
class VoteAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'question', 'choice', 'voted_at')
    list_filter = ('voted_at', 'question')
    search_fields = ('user__username', 'question__question_text', 'choice__choice_text')
    ordering = ('-voted_at',)
    readonly_fields = ('voted_at',)

    def has_change_permission(self, request, obj=None):
        # 투표 기록은 수정 불가
        return False
