{% extends 'layouts/main_layout.html' %}

{% block title %}설문조사 만들기 - Modern Survey{% endblock %}

{% block contents %}
    <!-- Page header -->
    <header class="py-5">
        <div class="container px-4 px-lg-5 my-5">
            <div class="text-center">
                <h1 class="display-4 fw-bolder">새 설문조사 만들기</h1>
                <p class="lead fw-normal text-muted mb-0">새로운 설문조사를 생성하여 의견을 수집해보세요</p>
            </div>
        </div>
    </header>

    <!-- Page content -->
    <section class="py-5">
        <div class="container px-4 px-lg-5">
            <div class="row gx-4 gx-lg-5 justify-content-center">
                <div class="col-lg-8 col-xl-6">
                    <!-- Create form card -->
                    <div class="card shadow border-0">
                        <div class="card-header bg-primary text-white">
                            <h4 class="mb-0">
                                <i class="bi bi-clipboard-plus me-2"></i>설문조사 정보 입력
                            </h4>
                        </div>

                        <div class="card-body p-4">
                            <form method="post">
                                {% csrf_token %}

                                <!-- Question input -->
                                <div class="mb-4">
                                    <label for="question_text" class="form-label fw-bold">
                                        <i class="bi bi-question-circle me-2"></i>질문
                                    </label>
                                    <input type="text"
                                           class="form-control form-control-lg"
                                           id="question_text"
                                           name="question_text"
                                           placeholder="설문조사 질문을 입력하세요"
                                           required>
                                    <div class="form-text">명확하고 이해하기 쉬운 질문을 작성해주세요.</div>
                                </div>

                                <!-- Choices input -->
                                <div class="mb-4">
                                    <label class="form-label fw-bold">
                                        <i class="bi bi-list-check me-2"></i>선택지
                                    </label>
                                    <div id="choice_layer">
                                        <div class="input-group mb-2">
                                            <span class="input-group-text fw-bold">1</span>
                                            <input type="text"
                                                   class="form-control"
                                                   name="choice_text"
                                                   placeholder="첫 번째 선택지를 입력하세요"
                                                   required>
                                        </div>
                                    </div>
                                    <div class="form-text mb-3">최소 2개 이상의 선택지를 추가해주세요.</div>

                                    <!-- Choice management buttons -->
                                    <div class="d-grid gap-2 d-md-flex">
                                        <button type="button" class="btn btn-outline-primary" onclick="addChoice()">
                                            <i class="bi bi-plus me-1"></i>선택지 추가
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="delChoice()">
                                            <i class="bi bi-dash me-1"></i>선택지 삭제
                                        </button>
                                    </div>
                                </div>

                                <!-- Action buttons -->
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <a href="{% url 'polls:list' %}" class="btn btn-outline-secondary me-md-2">
                                        <i class="bi bi-arrow-left me-2"></i>취소
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>설문조사 등록
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Help section -->
                    <div class="mt-4">
                        <div class="card border-0 bg-light">
                            <div class="card-body p-4">
                                <h5 class="fw-bold mb-3">
                                    <i class="bi bi-lightbulb me-2"></i>설문조사 작성 팁
                                </h5>
                                <ul class="mb-0">
                                    <li class="mb-2">질문은 명확하고 이해하기 쉽게 작성하세요</li>
                                    <li class="mb-2">선택지는 서로 배타적이고 포괄적이어야 합니다</li>
                                    <li class="mb-2">편향되지 않은 중립적인 표현을 사용하세요</li>
                                    <li>적절한 수의 선택지를 제공하세요 (보통 2-7개)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <script>
        let choiceCount = 1;

        function addChoice(){
            // 선택지 입력 폼을 추가하는 함수
            choiceCount++;

            const div = document.getElementById("choice_layer");
            const inputGroup = document.createElement("div");
            inputGroup.className = "input-group mb-2";

            inputGroup.innerHTML = `
                <span class="input-group-text fw-bold">${choiceCount}</span>
                <input type="text"
                       class="form-control"
                       name="choice_text"
                       placeholder="${choiceCount}번째 선택지를 입력하세요"
                       required>
            `;

            div.appendChild(inputGroup);
        }

        function delChoice(){
            // 선택지 입력 폼을 삭제하는 함수
            // 하나만 있을 경우 삭제하지 않는다
            const div = document.getElementById("choice_layer");
            if (div.children.length >= 2){
                div.removeChild(div.lastChild);
                choiceCount--;
            } else {
                // Modern Business 스타일의 알림
                const existingAlert = document.querySelector('.alert-warning');
                if (existingAlert) {
                    existingAlert.remove();
                }

                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-3';
                alertDiv.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <div>최소 1개의 선택지는 필요합니다.</div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                div.parentNode.insertBefore(alertDiv, div.nextSibling);

                // 5초 후 자동으로 알림 제거
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            }
        }
    </script>
{% endblock contents %}