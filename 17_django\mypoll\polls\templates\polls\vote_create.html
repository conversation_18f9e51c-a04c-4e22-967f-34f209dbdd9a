{% extends 'layouts/main_layout.html' %}

{% block title %}설문 등록{% endblock %}

{% block contents %}
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="text-center mb-4">
                <h1 class="display-5 fw-bold text-success">
                    <i class="bi bi-plus-circle me-3"></i>새 설문조사 만들기
                </h1>
                <p class="lead text-muted">새로운 설문조사를 생성하여 의견을 수집해보세요</p>
            </div>

            <div class="card shadow-lg border-0">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-clipboard-plus me-2"></i>설문조사 정보 입력
                    </h4>
                </div>

                <div class="card-body p-4">
                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-4">
                            <label for="question_text" class="form-label h5 text-primary">
                                <i class="bi bi-question-circle me-2"></i>질문
                            </label>
                            <input type="text"
                                   class="form-control form-control-lg"
                                   id="question_text"
                                   name="question_text"
                                   placeholder="설문조사 질문을 입력하세요"
                                   required>
                            <div class="form-text">명확하고 이해하기 쉬운 질문을 작성해주세요.</div>
                        </div>

                        <div class="mb-4">
                            <label class="form-label h5 text-primary">
                                <i class="bi bi-list-check me-2"></i>선택지
                            </label>
                            <div id="choice_layer">
                                <div class="input-group mb-2">
                                    <span class="input-group-text">1</span>
                                    <input type="text"
                                           class="form-control"
                                           name="choice_text"
                                           placeholder="첫 번째 선택지를 입력하세요"
                                           required>
                                </div>
                            </div>
                            <div class="form-text mb-3">최소 1개 이상의 선택지를 추가해주세요.</div>

                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="addChoice()">
                                    <i class="bi bi-plus me-1"></i>선택지 추가
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="delChoice()">
                                    <i class="bi bi-dash me-1"></i>선택지 삭제
                                </button>
                            </div>
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{% url 'polls:list' %}" class="btn btn-outline-secondary btn-custom me-md-2">
                                <i class="bi bi-arrow-left me-2"></i>취소
                            </a>
                            <button type="submit" class="btn btn-success btn-custom">
                                <i class="bi bi-check-circle me-2"></i>설문조사 등록
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script>
        let choiceCount = 1;

        function addChoice(){
            // 보기 입력 input form을 추가하는 함수
            choiceCount++;

            const div = document.getElementById("choice_layer");
            const inputGroup = document.createElement("div");
            inputGroup.className = "input-group mb-2";

            inputGroup.innerHTML = `
                <span class="input-group-text">${choiceCount}</span>
                <input type="text"
                       class="form-control"
                       name="choice_text"
                       placeholder="${choiceCount}번째 선택지를 입력하세요"
                       required>
            `;

            div.appendChild(inputGroup);
        }

        function delChoice(){
            // 보기 입력 input form을 삭제하는 함수
            // 하나만 있을 경우 삭제하지 않는다
            const div = document.getElementById("choice_layer");
            if (div.children.length >= 2){
                div.removeChild(div.lastChild);
                choiceCount--;
            } else {
                // Bootstrap 스타일의 알림
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-warning alert-dismissible fade show mt-2';
                alertDiv.innerHTML = `
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    최소 1개의 선택지는 필요합니다.
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;
                div.parentNode.insertBefore(alertDiv, div.nextSibling);

                // 3초 후 자동으로 알림 제거
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 3000);
            }
        }
    </script>
{% endblock contents %}
