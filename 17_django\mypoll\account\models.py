from django.db import models
from django.contrib.auth.models import AbstractUser

# Create your models here.

class User(AbstractUser):
    # username/password를 제외한 나머지 Field들을 정의
    name = models.CharField(
        verbose_name="이름", # Form관련 설정. ModelForm을 만들경우 form관련 설정을 Model field에 할 수 있다.
        max_length=50 # varchar(50)
        )
    email = models.EmailField(
        verbose_name="이메일", #검증 기능이 추가: 값이 이메일 형식(@를 포함하는지)인지 확인.
        max_length=100,
        unique=True,
    )
    birthday = models.DateField(
        verbose_name="생일",
        null=True,
        blank=True, # 입력폼 설정 - 빈값입력 가능 여부(default: False -required)
    )

    def __str__(self):
        return f"{self.username} - {self.name}"
    
# python manage.py makemigrations
# python manage.py migrate